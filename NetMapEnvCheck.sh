#!/bin/ash
#
# NetMapEnvCheck.sh - OpenWrt环境检测脚本
# 检测NatMaper运行所需的环境和依赖
# 适用于OpenWrt 24.x (nftables后端)
#
# 作者: NatMaper项目组
# 版本: 1.0
# 日期: 2025-01-11
#

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检测结果统计
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0
WARNING_CHECKS=0

# 打印函数
print_header() {
    echo -e "${BLUE}================================================${NC}"
    echo -e "${BLUE}    NetMaper 环境检测脚本 v1.0${NC}"
    echo -e "${BLUE}    OpenWrt 24.x 兼容性检测${NC}"
    echo -e "${BLUE}================================================${NC}"
    echo ""
}

print_check() {
    local status=$1
    local message=$2
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    case $status in
        "PASS")
            echo -e "[${GREEN}✓${NC}] $message"
            PASSED_CHECKS=$((PASSED_CHECKS + 1))
            ;;
        "FAIL")
            echo -e "[${RED}✗${NC}] $message"
            FAILED_CHECKS=$((FAILED_CHECKS + 1))
            ;;
        "WARN")
            echo -e "[${YELLOW}!${NC}] $message"
            WARNING_CHECKS=$((WARNING_CHECKS + 1))
            ;;
        "INFO")
            echo -e "[${BLUE}i${NC}] $message"
            ;;
    esac
}

print_section() {
    echo ""
    echo -e "${BLUE}--- $1 ---${NC}"
}

print_summary() {
    echo ""
    echo -e "${BLUE}================================================${NC}"
    echo -e "${BLUE}              检测结果汇总${NC}"
    echo -e "${BLUE}================================================${NC}"
    echo -e "总检测项: $TOTAL_CHECKS"
    echo -e "${GREEN}通过: $PASSED_CHECKS${NC}"
    echo -e "${RED}失败: $FAILED_CHECKS${NC}"
    echo -e "${YELLOW}警告: $WARNING_CHECKS${NC}"
    echo ""
    
    if [ $FAILED_CHECKS -eq 0 ]; then
        echo -e "${GREEN}🎉 环境检测通过！NatMaper应该能正常运行。${NC}"
    else
        echo -e "${RED}❌ 发现 $FAILED_CHECKS 个问题，需要修复后才能正常使用NatMaper。${NC}"
    fi
}

# 检测系统基本信息
check_system_info() {
    print_section "系统信息检测"
    
    # OpenWrt版本
    if [ -f /etc/openwrt_release ]; then
        local version=$(grep DISTRIB_RELEASE /etc/openwrt_release | cut -d"'" -f2)
        print_check "INFO" "OpenWrt版本: $version"
        
        # 检测是否为24.x版本
        case $version in
            24.*)
                print_check "WARN" "检测到OpenWrt 24.x版本，使用nftables后端"
                ;;
            *)
                print_check "INFO" "OpenWrt版本: $version"
                ;;
        esac
    else
        print_check "WARN" "无法检测OpenWrt版本"
    fi
    
    # 内核版本
    local kernel=$(uname -r)
    print_check "INFO" "内核版本: $kernel"
    
    # 架构
    local arch=$(uname -m)
    print_check "INFO" "系统架构: $arch"
}

# 检测iptables
check_iptables() {
    print_section "iptables检测"
    
    # iptables是否安装
    if command -v iptables >/dev/null 2>&1; then
        local version=$(iptables -V 2>/dev/null)
        print_check "PASS" "iptables已安装: $version"
        
        # 检测后端类型
        if echo "$version" | grep -q "nf_tables"; then
            print_check "INFO" "使用nftables后端"
        else
            print_check "INFO" "使用legacy后端"
        fi
    else
        print_check "FAIL" "iptables未安装"
        return 1
    fi
    
    # 检测NAT表访问
    if iptables -t nat -L >/dev/null 2>&1; then
        print_check "PASS" "NAT表可访问"
    else
        print_check "FAIL" "NAT表不可访问"
    fi
}

# 检测内核模块
check_kernel_modules() {
    print_section "内核模块检测"
    
    # 检测关键模块
    local modules="nf_nat nf_conntrack x_tables"
    for module in $modules; do
        if lsmod | grep -q "^$module"; then
            print_check "PASS" "模块 $module 已加载"
        else
            print_check "WARN" "模块 $module 未加载"
        fi
    done
    
    # 检测iptables扩展模块
    local xt_modules="xt_nat xt_comment xt_conntrack"
    for module in $xt_modules; do
        if lsmod | grep -q "^$module"; then
            print_check "PASS" "扩展模块 $module 已加载"
        else
            print_check "WARN" "扩展模块 $module 未加载"
        fi
    done
}

# 检测软件包
check_packages() {
    print_section "软件包检测"
    
    # 检测必需包
    local packages="iptables iptables-mod-extra kmod-ipt-nat"
    for package in $packages; do
        if opkg list-installed | grep -q "^$package "; then
            print_check "PASS" "软件包 $package 已安装"
        else
            print_check "FAIL" "软件包 $package 未安装"
        fi
    done
}

# 检测扩展库文件
check_extension_libs() {
    print_section "扩展库文件检测"
    
    # 检测xtables库目录
    if [ -d /usr/lib/xtables ]; then
        print_check "PASS" "xtables库目录存在"
        
        # 检测关键库文件
        local libs="libxt_comment.so libxt_conntrack.so libxt_nat.so"
        for lib in $libs; do
            if [ -f "/usr/lib/xtables/$lib" ]; then
                print_check "PASS" "库文件 $lib 存在"
            else
                print_check "WARN" "库文件 $lib 缺失"
            fi
        done
    else
        print_check "FAIL" "xtables库目录不存在"
    fi
}

# 功能测试
check_functionality() {
    print_section "功能测试"
    
    # 测试DNAT规则创建
    local test_port=19999
    local test_ip="***********"
    
    print_check "INFO" "测试DNAT规则创建..."
    
    # 尝试创建测试规则
    if iptables -t nat -A PREROUTING -p tcp --dport $test_port \
        -m comment --comment "netmap_test" \
        -j DNAT --to-destination $test_ip:22 2>/dev/null; then
        
        print_check "PASS" "DNAT规则创建成功"
        
        # 检查规则是否真的存在
        if iptables -t nat -L PREROUTING -n | grep -q "$test_port"; then
            print_check "PASS" "DNAT规则验证成功"
        else
            print_check "WARN" "DNAT规则创建但验证失败"
        fi
        
        # 清理测试规则
        iptables -t nat -D PREROUTING -p tcp --dport $test_port \
            -m comment --comment "netmap_test" \
            -j DNAT --to-destination $test_ip:22 2>/dev/null
        print_check "INFO" "测试规则已清理"
        
    else
        print_check "FAIL" "DNAT规则创建失败"
    fi
}

# 检测网络接口
check_network_interfaces() {
    print_section "网络接口检测"
    
    # 检测WAN接口
    local wan_interfaces=$(uci show network | grep "proto='dhcp'" | cut -d'.' -f2 | cut -d'=' -f1)
    if [ -n "$wan_interfaces" ]; then
        for iface in $wan_interfaces; do
            print_check "PASS" "WAN接口: $iface"
        done
    else
        print_check "WARN" "未检测到DHCP WAN接口"
    fi
    
    # 检测LAN接口
    if uci show network | grep -q "proto='static'"; then
        print_check "PASS" "检测到静态LAN接口"
    else
        print_check "WARN" "未检测到静态LAN接口"
    fi
}

# 提供修复建议
provide_fix_suggestions() {
    if [ $FAILED_CHECKS -gt 0 ] || [ $WARNING_CHECKS -gt 0 ]; then
        echo ""
        echo -e "${YELLOW}=== 修复建议 ===${NC}"
        echo ""
        
        echo -e "${YELLOW}1. 安装缺失的软件包:${NC}"
        echo "   opkg update"
        echo "   opkg install iptables iptables-mod-extra kmod-ipt-nat"
        echo ""
        
        echo -e "${YELLOW}2. 加载必需的内核模块:${NC}"
        echo "   modprobe xt_nat"
        echo "   modprobe xt_comment"
        echo ""
        
        echo -e "${YELLOW}3. 设置模块自动加载:${NC}"
        echo "   mkdir -p /etc/modules-load.d"
        echo "   echo 'xt_nat' >> /etc/modules-load.d/iptables.conf"
        echo "   echo 'xt_comment' >> /etc/modules-load.d/iptables.conf"
        echo ""
        
        echo -e "${YELLOW}4. 重新运行检测:${NC}"
        echo "   ./NetMapEnvCheck.sh"
    fi
}

# 主函数
main() {
    print_header
    
    check_system_info
    check_iptables
    check_kernel_modules
    check_packages
    check_extension_libs
    check_network_interfaces
    check_functionality
    
    print_summary
    provide_fix_suggestions
    
    echo ""
    echo -e "${BLUE}检测完成！${NC}"
    
    # 返回适当的退出码
    if [ $FAILED_CHECKS -eq 0 ]; then
        exit 0
    else
        exit 1
    fi
}

# 执行主函数
main "$@"
