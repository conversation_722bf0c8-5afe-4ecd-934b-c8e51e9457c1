#!/bin/ash
# NatMaper v2.1 - Professional NAT Port Mapping Manager
# Enhanced batch port forwarding management for OpenWrt
# Features: Real-time display, IP sorting, color output, unified config export
# Author: Augment Agent | Compatible: OpenWrt 24.x | License: MIT
#
# v2.1 Updates (2025-01-10):
# • Fixed subshell variable scope issues in delete functions
# • Improved delete operation accuracy and feedback
# • Enhanced error handling for empty rule numbers
# • Fixed port range deletion counting problem

# Colors for enhanced user experience
RED='\033[0;31m'; GREEN='\033[0;32m'; YELLOW='\033[1;33m'
CYAN='\033[0;36m'; WHITE='\033[1;37m'; BLUE='\033[0;34m'
MAGENTA='\033[0;35m'; NC='\033[0m'

# Common UI elements
SEPARATOR="━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# Output functions with consistent formatting
info() { printf "${CYAN}ℹ${NC} %s\n" "$1"; }
ok() { printf "${GREEN}✓${NC} %s\n" "$1"; }
err() { printf "${RED}✗${NC} %s\n" "$1"; }
warn() { printf "${YELLOW}⚠${NC} %s\n" "$1"; }
title() { printf "\n${WHITE}=== %s ===${NC}\n" "$1"; }
separator() { printf "%s\n" "$SEPARATOR"; }

# Input validation helper
validate_input() {
    local value="$1" name="$2" pattern="$3"
    [ -z "$value" ] && { err "❌ ${name}不能为空"; return 1; }
    [ -n "$pattern" ] && ! echo "$value" | grep -q "$pattern" && { err "❌ ${name}格式不正确"; return 1; }
    return 0
}

# Common configuration parsing helper
parse_config_line() {
    local line="$1" key value
    echo "$line" | grep -q "=" || return 1
    key=$(echo "$line" | cut -d'=' -f1 | tr -d ' ')
    value=$(echo "$line" | cut -d'=' -f2- | sed 's/^[ "]*//;s/[ "]*$//')
    echo "$key|$value"
}

# Standardized date format
get_timestamp() {
    date '+%Y-%m-%d %H:%M:%S'
}

# Safe arithmetic function to avoid "bad number" errors
safe_calc() {
    local expr="$1"
    local default="${2:-0}"

    # Validate input contains only numbers and basic operators
    if echo "$expr" | grep -q '^[0-9+*/() -]\+$'; then
        # Use sh-compatible arithmetic evaluation for BusyBox
        if result=$(sh -c "echo \$(($expr))" 2>/dev/null) && [ -n "$result" ]; then
            echo "$result"
        else
            echo "$default"
        fi
    else
        echo "$default"
    fi
}



# WAN Interface Detection and Management Functions
detect_wan_interfaces() {
    info "Detecting available WAN interfaces..."
    local interfaces=""
    local uci_interfaces=""

    # Try UCI first (OpenWrt standard method)
    if command -v uci >/dev/null 2>&1; then
        info "Using UCI to detect WAN interfaces..."
        # Check interfaces in WAN zone or with WAN-like protocols
        for iface in $(uci show network 2>/dev/null | grep "\.proto=" | grep -E "(dhcp|static|pppoe)" | cut -d'.' -f2 | cut -d'=' -f1); do
            # Check if interface is in WAN zone or has WAN-like name
            local zone=$(uci show firewall 2>/dev/null | grep "network.*$iface" | grep "zone.*wan" | head -1)
            if [ -n "$zone" ] || echo "$iface" | grep -qE "(wan|eth|ppp)"; then
                local ifname=$(uci get network.${iface}.ifname 2>/dev/null || uci get network.${iface}.device 2>/dev/null || echo "$iface")
                if [ -n "$ifname" ] && ip link show "$ifname" >/dev/null 2>&1; then
                    if ip link show "$ifname" | grep -q "state UP"; then
                        local ip=$(ip addr show "$ifname" 2>/dev/null | grep 'inet ' | head -1 | awk '{print $2}' | cut -d'/' -f1)
                        if [ -n "$ip" ]; then
                            interfaces="$interfaces $ifname"
                            uci_interfaces="$uci_interfaces $iface"
                            printf "  ${GREEN}✓${NC} %s (UCI: %s): %s\n" "$ifname" "$iface" "$ip"
                        fi
                    fi
                fi
            fi
        done
    fi

    # Always also check physical interfaces directly
    info "Checking physical WAN interfaces..."
    for iface in $(ls /sys/class/net/ 2>/dev/null); do
        # Skip loopback and internal interfaces
        case "$iface" in
            lo|br-lan|wlan*) continue ;;
            wan*|eth*|br-wan*|pppoe*|3g*|4g*|lte*)
                # Skip if already found via UCI
                if echo "$interfaces" | grep -q "$iface"; then
                    continue
                fi
                # Check if interface is up
                if ip link show "$iface" 2>/dev/null | grep -q "state UP"; then
                    # Check if interface has an IP address
                    if ip addr show "$iface" 2>/dev/null | grep -q "inet "; then
                        local ip=$(ip addr show "$iface" | grep "inet " | head -1 | awk '{print $2}' | cut -d'/' -f1)
                        interfaces="$interfaces $iface"
                        printf "  ${GREEN}✓${NC} %s: %s\n" "$iface" "$ip"
                    else
                        printf "  ${YELLOW}⚠${NC} %s: UP but no IP\n" "$iface"
                    fi
                else
                    printf "  ${RED}✗${NC} %s: DOWN\n" "$iface"
                fi
                ;;
        esac
    done

    if [ -z "$interfaces" ]; then
        err "No active WAN interfaces found!"
        return 1
    fi

    printf "\n${CYAN}可用WAN接口 (物理设备名):${NC}$interfaces\n"
    if [ -n "$uci_interfaces" ]; then
        printf "${CYAN}UCI逻辑接口名:${NC}$uci_interfaces\n"
        printf "${YELLOW}💡 提示: 可以使用UCI逻辑名或物理设备名${NC}\n"
    fi
    echo "$interfaces"
    return 0
}

# Resolve UCI logical interface name to physical interface name
resolve_wan_interface() {
    local interface="$1"
    local physical_interface="$interface"

    if [ -z "$interface" ]; then
        echo ""
        return 1
    fi

    # Try to resolve UCI logical name to physical interface
    if command -v uci >/dev/null 2>&1; then
        local uci_ifname=$(uci get network.${interface}.ifname 2>/dev/null || uci get network.${interface}.device 2>/dev/null)
        if [ -n "$uci_ifname" ]; then
            physical_interface="$uci_ifname"
        fi
    fi

    # Check if physical interface exists
    if ! ip link show "$physical_interface" >/dev/null 2>&1; then
        # If UCI resolution failed, try the original interface name
        if [ "$physical_interface" != "$interface" ] && ip link show "$interface" >/dev/null 2>&1; then
            physical_interface="$interface"
        else
            echo ""
            return 1
        fi
    fi

    echo "$physical_interface"
    return 0
}

validate_wan_interface() {
    local interface="$1"
    local physical_interface

    if [ -z "$interface" ]; then
        err "Interface name cannot be empty"
        return 1
    fi

    # Resolve to physical interface
    physical_interface=$(resolve_wan_interface "$interface")
    if [ -z "$physical_interface" ]; then
        err "Interface '$interface' does not exist"
        return 1
    fi

    if ! ip link show "$physical_interface" | grep -q "state UP"; then
        warn "Interface '$interface' (physical: '$physical_interface') is not UP"
        return 1
    fi

    ok "Interface '$interface' (physical: '$physical_interface') is valid and active"
    return 0
}

get_wan_interface_ip() {
    local interface="$1"
    local physical_interface

    # Resolve to physical interface
    physical_interface=$(resolve_wan_interface "$interface")
    if [ -z "$physical_interface" ]; then
        echo "No IP"
        return 1
    fi

    # Get IP from physical interface
    local ip=$(ip addr show "$physical_interface" 2>/dev/null | grep "inet " | head -1 | awk '{print $2}' | cut -d'/' -f1)

    if [ -z "$ip" ]; then
        echo "No IP"
        return 1
    fi

    # Check if it's a private IP (might be behind NAT)
    case "$ip" in
        192.168.*|10.*|172.1[6-9].*|172.2[0-9].*|172.3[01].*)
            # Try to get public IP through this interface
            local public_ip=$(timeout 5 curl -s --interface "$physical_interface" ifconfig.me 2>/dev/null || echo "")
            if [ -n "$public_ip" ] && [ "$public_ip" != "$ip" ]; then
                echo "$ip (Public: $public_ip)"
            else
                echo "$ip (Private)"
            fi
            ;;
        *)
            echo "$ip (Public)"
            ;;
    esac
    return 0
}

# Enhanced rule addition with WAN interface binding
add_port_forwarding_rule() {
    local wan_interface="$1"
    local ext_port="$2"
    local vm_ip="$3"
    local int_port="$4"
    local network_id="${5:-default}"
    local service_name="${6:-port}"
    local physical_interface=""

    # Resolve and validate WAN interface if specified
    if [ -n "$wan_interface" ] && [ "$wan_interface" != "auto" ]; then
        physical_interface=$(resolve_wan_interface "$wan_interface")
        if [ -z "$physical_interface" ]; then
            warn "WAN interface '$wan_interface' not available, using default routing"
            wan_interface=""
            physical_interface=""
        elif ! validate_wan_interface "$wan_interface" >/dev/null 2>&1; then
            warn "WAN interface '$wan_interface' not active, using default routing"
            wan_interface=""
            physical_interface=""
        fi
    fi

    # Build iptables command with optional WAN interface binding
    local nat_tcp_cmd="iptables -t nat -A BATCH_NAT"
    local nat_udp_cmd="iptables -t nat -A BATCH_NAT"
    local filter_tcp_cmd="iptables -A BATCH_FILTER"
    local filter_udp_cmd="iptables -A BATCH_FILTER"

    # Add WAN interface binding if specified and valid - use physical interface name
    if [ -n "$physical_interface" ]; then
        nat_tcp_cmd="$nat_tcp_cmd -i $physical_interface"
        nat_udp_cmd="$nat_udp_cmd -i $physical_interface"
        filter_tcp_cmd="$filter_tcp_cmd -i $physical_interface"
        filter_udp_cmd="$filter_udp_cmd -i $physical_interface"
    fi

    # Add rule comments for management - use original interface name for identification
    # Handle empty wan_interface case and sanitize for iptables comment compatibility
    local wan_interface_safe="${wan_interface:-none}"
    # Remove any problematic characters from interface name for comment
    wan_interface_safe=$(echo "$wan_interface_safe" | tr -cd 'a-zA-Z0-9._-')
    local comment="natmap-${network_id}-${wan_interface_safe}-${ext_port}"

    # Complete the commands (removed comment dependency for compatibility)
    nat_tcp_cmd="$nat_tcp_cmd -p tcp --dport $ext_port -j DNAT --to-destination $vm_ip:$int_port"
    nat_udp_cmd="$nat_udp_cmd -p udp --dport $ext_port -j DNAT --to-destination $vm_ip:$int_port"
    filter_tcp_cmd="$filter_tcp_cmd -p tcp -d $vm_ip --dport $int_port -j ACCEPT"
    filter_udp_cmd="$filter_udp_cmd -p udp -d $vm_ip --dport $int_port -j ACCEPT"

    # Execute the commands
    if eval "$nat_tcp_cmd" 2>/dev/null && \
       eval "$nat_udp_cmd" 2>/dev/null && \
       eval "$filter_tcp_cmd" 2>/dev/null && \
       eval "$filter_udp_cmd" 2>/dev/null; then
        return 0
    else
        return 1
    fi
}

# Interface-based rule management functions
query_rules_by_interface() {
    local interface="$1"
    local physical_interface

    if [ -z "$interface" ]; then
        err "Interface name is required"
        return 1
    fi

    # Resolve UCI logical name to physical interface
    physical_interface=$(resolve_wan_interface "$interface")
    if [ -n "$physical_interface" ] && [ "$physical_interface" != "$interface" ]; then
        info "Resolved UCI interface '$interface' to physical interface '$physical_interface'"
    fi

    title "Port Forwarding Rules for Interface: $interface"

    # Check if interface exists and get its IP
    local interface_ip=$(get_wan_interface_ip "$interface" 2>/dev/null)
    if [ $? -eq 0 ]; then
        printf "${GREEN}Interface Status:${NC} %s - %s\n\n" "$interface" "$interface_ip"
    else
        printf "${YELLOW}Interface Status:${NC} %s - Not available\n\n" "$interface"
    fi

    # Query NAT rules - try multiple methods to find interface-related rules
    local nat_rules=""

    # Method 1: Check for rules with interface binding (-i parameter)
    nat_rules=$(iptables -t nat -L BATCH_NAT -n --line-numbers 2>/dev/null | grep "^[0-9]" | grep -E " ($interface|$physical_interface) ")

    # Method 2: Check for rules with interface in comment (natmap-xxx-interface-xxx)
    if [ -z "$nat_rules" ]; then
        # Use word boundaries to ensure exact interface name matching
        nat_rules=$(iptables -t nat -L BATCH_NAT -n --line-numbers 2>/dev/null | grep "^[0-9]" | grep -E "natmap-[^-]*-(${interface}|${physical_interface}|none)-[0-9]+")
    fi

    # Method 3: If no interface-specific rules found, show all rules but warn
    if [ -z "$nat_rules" ]; then
        nat_rules=$(iptables -t nat -L BATCH_NAT -n --line-numbers 2>/dev/null | grep "^[0-9]" | grep "DNAT")
        if [ -n "$nat_rules" ]; then
            warn "No interface-specific rules found for '$interface'. Showing all rules:"
            info "Rules may not be bound to specific interfaces or use different interface names"
        fi
    fi

    # Fallback to PREROUTING if BATCH_NAT doesn't exist
    if [ -z "$nat_rules" ]; then
        nat_rules=$(iptables -t nat -L PREROUTING -n --line-numbers 2>/dev/null | grep -E " ($interface|$physical_interface) " | grep "DNAT")
    fi

    if [ -z "$nat_rules" ]; then
        warn "No port forwarding rules found for interface '$interface' (physical: '$physical_interface')"
        info "Try checking with: iptables -t nat -L BATCH_NAT -n | grep '$physical_interface'"
        info "Or check all rules with option 2 (显示当前规则)"
        return 0
    fi

    printf "${CYAN}📋 NAT Rules (PREROUTING):${NC}\n"
    printf "%-4s %-8s %-12s %-15s %-12s %-12s %-20s\n" "Line" "Proto" "Ext Port" "Target IP" "Int Port" "Interface" "Comment"
    separator

    echo "$nat_rules" | while IFS= read -r rule; do
        local line_num=$(echo "$rule" | awk '{print $1}')
        local proto=$(echo "$rule" | awk '{print $2}')
        local ext_port=$(echo "$rule" | grep -o 'dpt:[0-9]*' | cut -d':' -f2)
        local target=$(echo "$rule" | grep -o 'to:[0-9.]*:[0-9]*' | cut -d':' -f2-3)
        local target_ip=$(echo "$target" | cut -d':' -f1)
        local int_port=$(echo "$target" | cut -d':' -f2)
        local comment=$(echo "$rule" | grep -o 'natmap-[^[:space:]]*' || echo "")

        # Extract interface from rule or comment
        local rule_interface=""
        if echo "$rule" | grep -q " in "; then
            rule_interface=$(echo "$rule" | sed 's/.* in \([^ ]*\) .*/\1/')
        elif [ -n "$comment" ]; then
            # Extract interface from comment: natmap-xxx-interface-xxx
            rule_interface=$(echo "$comment" | sed 's/natmap-[^-]*-\([^-]*\)-.*/\1/' 2>/dev/null || echo "")
        fi
        [ -z "$rule_interface" ] && rule_interface="any"

        printf "%-4s %-8s %-12s %-15s %-12s %-12s %-20s\n" "$line_num" "$proto" "$ext_port" "$target_ip" "$int_port" "$rule_interface" "$comment"
    done

    printf "\n${GREEN}Total rules found:${NC} $(echo "$nat_rules" | wc -l)\n"
}

delete_rules_by_interface() {
    local interface="$1"
    local physical_interface

    if [ -z "$interface" ]; then
        err "Interface name is required"
        return 1
    fi

    # Resolve UCI logical name to physical interface
    physical_interface=$(resolve_wan_interface "$interface")
    if [ -n "$physical_interface" ] && [ "$physical_interface" != "$interface" ]; then
        info "Resolved UCI interface '$interface' to physical interface '$physical_interface'"
    fi

    title "Delete Port Forwarding Rules for Interface: $interface"

    # Get rules to delete - use same multi-method approach as query function
    local nat_rules=""

    # Method 1: Check for rules with interface binding (-i parameter)
    nat_rules=$(iptables -t nat -L BATCH_NAT -n --line-numbers 2>/dev/null | grep "^[0-9]" | grep -E " ($interface|$physical_interface) ")

    # Method 2: Check for rules with interface in comment (natmap-xxx-interface-xxx)
    if [ -z "$nat_rules" ]; then
        # Use word boundaries to ensure exact interface name matching
        nat_rules=$(iptables -t nat -L BATCH_NAT -n --line-numbers 2>/dev/null | grep "^[0-9]" | grep -E "natmap-[^-]*-(${interface}|${physical_interface}|none)-[0-9]+")
    fi

    # Method 3: If no interface-specific rules found, show all rules but warn
    if [ -z "$nat_rules" ]; then
        nat_rules=$(iptables -t nat -L BATCH_NAT -n --line-numbers 2>/dev/null | grep "^[0-9]" | grep "DNAT")
        if [ -n "$nat_rules" ]; then
            warn "No interface-specific rules found for '$interface'."
            warn "Found general port forwarding rules. This will delete ALL rules!"
            printf "Are you sure you want to delete ALL port forwarding rules? (y/N): "
            read -r confirm
            if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
                info "Operation cancelled."
                return 0
            fi
        fi
    fi

    # Fallback to PREROUTING if BATCH_NAT doesn't exist
    if [ -z "$nat_rules" ]; then
        nat_rules=$(iptables -t nat -L PREROUTING -n --line-numbers 2>/dev/null | grep -E " ($interface|$physical_interface) " | grep "DNAT")
    fi

    # Get FILTER rules - check both interface names and comments
    local filter_rules=$(iptables -L BATCH_FILTER -n --line-numbers 2>/dev/null | grep -E "natmap-[^-]*-(${interface}|${physical_interface}|none)-[0-9]+-fwd" | grep "ACCEPT")
    if [ -z "$filter_rules" ]; then
        filter_rules=$(iptables -L BATCH_FILTER -n --line-numbers 2>/dev/null | grep "natmap-.*-fwd")
        if [ -z "$filter_rules" ]; then
            filter_rules=$(iptables -L FORWARD -n --line-numbers 2>/dev/null | grep "natmap-.*-fwd")
        fi
    fi

    if [ -z "$nat_rules" ]; then
        warn "No port forwarding rules found for interface '$interface' (physical: '$physical_interface')"
        info "Try checking all rules with option 2 (显示当前规则)"
        return 0
    fi

    local rule_count=$(echo "$nat_rules" | wc -l)
    printf "Found %s rules for interface '%s'\n\n" "$rule_count" "$interface"

    # Show rules to be deleted
    printf "${YELLOW}Rules to be deleted:${NC}\n"
    echo "$nat_rules" | head -5 | while IFS= read -r rule; do
        local ext_port=$(echo "$rule" | grep -o 'dpt:[0-9]*' | cut -d':' -f2)
        local target=$(echo "$rule" | grep -o 'to:[0-9.]*:[0-9]*' | cut -d':' -f2-3)
        printf "  Port %s → %s\n" "$ext_port" "$target"
    done

    if [ "$rule_count" -gt 5 ]; then
        printf "  ... and %s more rules\n" "$((rule_count - 5))"
    fi

    printf "\n${RED}⚠️  This will delete ALL port forwarding rules for interface '%s'${NC}\n" "$interface"
    printf "Are you sure? (y/N): "
    read -r confirm

    if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
        info "Operation cancelled"
        return 0
    fi

    # Delete rules (from highest line number to lowest to avoid renumbering issues)
    local deleted=0

    # Check if we found rules in BATCH_NAT or PREROUTING and delete accordingly
    local nat_chain="PREROUTING"
    if iptables -t nat -L BATCH_NAT -n >/dev/null 2>&1 && echo "$nat_rules" | head -1 | grep -q "BATCH_NAT"; then
        nat_chain="BATCH_NAT"
    fi

    # Fix: Use for loop instead of while pipe to avoid subshell variable scope issue
    local rule_lines=$(echo "$nat_rules" | sort -nr | awk '{print $1}')
    for line_num in $rule_lines; do
        if [ -n "$line_num" ] && iptables -t nat -D "$nat_chain" "$line_num" 2>/dev/null; then
            deleted=$((deleted + 1))
        fi
    done

    # Delete corresponding filter rules - check BATCH_FILTER first
    local filter_deleted=0
    local filter_chain="FORWARD"
    if iptables -L BATCH_FILTER -n >/dev/null 2>&1 && echo "$filter_rules" | head -1 | grep -q "BATCH_FILTER"; then
        filter_chain="BATCH_FILTER"
    fi

    # Fix: Use for loop instead of while pipe to avoid subshell variable scope issue
    local filter_lines=$(echo "$filter_rules" | sort -nr | awk '{print $1}')
    for line_num in $filter_lines; do
        if [ -n "$line_num" ] && iptables -D "$filter_chain" "$line_num" 2>/dev/null; then
            filter_deleted=$((filter_deleted + 1))
        fi
    done

    ok "Deleted $deleted NAT rules and $filter_deleted filter rules for interface '$interface'"

    # Update configuration file - remove networks associated with this interface
    if [ -f "$CONFIG_FILE" ] && [ "$deleted" -gt 0 ]; then
        info "🔄 正在同步配置文件..."

        # Find networks using this interface and remove them
        local networks_to_remove=$(grep -B5 -A5 "wan_interface = \"$interface\"" "$CONFIG_FILE" | grep "^network = " | cut -d'"' -f2)

        for network_prefix in $networks_to_remove; do
            if [ -n "$network_prefix" ]; then
                remove_network_from_config "$network_prefix"
            fi
        done
    fi

    # Save rules for persistence
    save_rules
}

export_rules_by_interface() {
    local interface="$1"
    local output_file="${2:-natmap_${interface}_rules.txt}"
    local physical_interface

    if [ -z "$interface" ]; then
        err "Interface name is required"
        return 1
    fi

    # Resolve UCI logical name to physical interface
    physical_interface=$(resolve_wan_interface "$interface")
    if [ -n "$physical_interface" ] && [ "$physical_interface" != "$interface" ]; then
        info "Resolved UCI interface '$interface' to physical interface '$physical_interface'"
    fi

    title "Export Port Forwarding Rules for Interface: $interface"

    # Get interface IP for reference
    local interface_ip=$(get_wan_interface_ip "$interface" 2>/dev/null || echo "Unknown")

    # Create export file
    cat > "$output_file" << EOF
; NatMap Port Forwarding Rules Export
; Interface: $interface (Physical: $physical_interface)
; IP Address: $interface_ip
; Export Date: $(get_timestamp)
; Generated by NatMaper.sh

EOF

    # Export NAT rules - use same multi-method approach as query function
    local nat_rules=""
    local nat_chain="BATCH_NAT"

    # Method 1: Check for rules with interface binding (-i parameter)
    nat_rules=$(iptables -t nat -L BATCH_NAT -n --line-numbers 2>/dev/null | grep "^[0-9]" | grep -E " ($interface|$physical_interface) ")

    # Method 2: Check for rules with interface in comment (natmap-xxx-interface-xxx)
    if [ -z "$nat_rules" ]; then
        # Use word boundaries to ensure exact interface name matching
        nat_rules=$(iptables -t nat -L BATCH_NAT -n --line-numbers 2>/dev/null | grep "^[0-9]" | grep -E "natmap-[^-]*-(${interface}|${physical_interface}|none)-[0-9]+")
    fi

    # Method 3: If no interface-specific rules found, export all rules but note in file
    if [ -z "$nat_rules" ]; then
        nat_rules=$(iptables -t nat -L BATCH_NAT -n --line-numbers 2>/dev/null | grep "^[0-9]" | grep "DNAT")
        if [ -n "$nat_rules" ]; then
            echo "; WARNING: No interface-specific rules found for '$interface'" >> "$output_file"
            echo "; Exporting all port forwarding rules instead" >> "$output_file"
            echo "" >> "$output_file"
        fi
    fi

    # Fallback to PREROUTING if BATCH_NAT doesn't exist
    if [ -z "$nat_rules" ]; then
        nat_rules=$(iptables -t nat -L PREROUTING -n --line-numbers 2>/dev/null | grep -E " ($interface|$physical_interface) " | grep "DNAT")
        nat_chain="PREROUTING"
    fi

    echo "# NAT Rules ($nat_chain)" >> "$output_file"
    echo "$nat_rules" | while IFS= read -r rule; do
        local ext_port=$(echo "$rule" | grep -o 'dpt:[0-9]*' | cut -d':' -f2)
        local target=$(echo "$rule" | grep -o 'to:[0-9.]*:[0-9]*' | cut -d':' -f2-3)
        local target_ip=$(echo "$target" | cut -d':' -f1)
        local int_port=$(echo "$target" | cut -d':' -f2)
        local proto=$(echo "$rule" | awk '{print $2}')

        echo "iptables -t nat -A PREROUTING -i $interface -p $proto --dport $ext_port -j DNAT --to-destination $target_ip:$int_port" >> "$output_file"
    done

    echo "" >> "$output_file"
    echo "# Filter Rules (FORWARD)" >> "$output_file"
    iptables -L FORWARD -n --line-numbers 2>/dev/null | grep "natmap-.*-fwd" | while IFS= read -r rule; do
        # Extract rule details and reconstruct command
        echo "# $rule" >> "$output_file"
    done

    local rule_count=$(echo "$nat_rules" | wc -l)
    if [ -z "$nat_rules" ]; then
        rule_count=0
    fi

    printf "\n${GREEN}✓ Exported %s rules to '%s'${NC}\n" "$rule_count" "$output_file"
    printf "Interface: %s (%s)\n" "$interface" "$interface_ip"
}

# Configuration file path (unified multi-network format)
CONFIG_FILE="./multi_network_config.conf"

# Environment check and auto-setup
check_env() {
    # Skip root check for config-only operations
    case "$1" in
        "config_only"|"show_multi_config"|"export"|"test")
            return 0
            ;;
        "")
            # Continue to root check for interactive mode
            ;;
    esac

    [ "$(id -u)" != "0" ] && { err "Root permission required"; exit 1; }
    command -v iptables >/dev/null || { info "Installing iptables..."; opkg update && opkg install iptables; }
}

# Initialize iptables chains
init_chains() {
    iptables -t nat -N BATCH_NAT 2>/dev/null || true
    iptables -N BATCH_FILTER 2>/dev/null || true
    iptables -t nat -C PREROUTING -j BATCH_NAT 2>/dev/null || iptables -t nat -I PREROUTING -j BATCH_NAT
    iptables -C FORWARD -j BATCH_FILTER 2>/dev/null || iptables -I FORWARD -j BATCH_FILTER
}

# Save iptables rules to make them persistent
save_rules() {
    local backup_dir="/etc/natmap"
    local rules_file="$backup_dir/iptables.rules"

    # Create backup directory
    mkdir -p "$backup_dir"

    # Save current iptables rules
    iptables-save > "$rules_file" 2>/dev/null

    # Create startup script for OpenWrt
    cat > "/etc/init.d/natmap" << 'EOF'
#!/bin/sh /etc/rc.common

START=99
STOP=10

start() {
    # Restore iptables rules
    if [ -f /etc/natmap/iptables.rules ]; then
        iptables-restore < /etc/natmap/iptables.rules
        echo "NatMap: iptables rules restored"
    fi
}

stop() {
    # Clear NatMap chains
    iptables -t nat -F BATCH_NAT 2>/dev/null || true
    iptables -F BATCH_FILTER 2>/dev/null || true
    echo "NatMap: rules cleared"
}

restart() {
    stop
    start
}
EOF

    # Make startup script executable
    chmod +x "/etc/init.d/natmap"

    # Enable the service
    /etc/init.d/natmap enable 2>/dev/null || true

    ok "Rules saved and persistence enabled"
    info "Rules will be restored automatically after reboot"
}

# Restore iptables rules from backup
restore_rules_internal() {
    local backup_dir="/etc/natmap"
    local rules_file="$backup_dir/iptables.rules"

    if [ ! -f "$rules_file" ]; then
        warn "No backup rules found at $rules_file"
        return 1
    fi

    # Restore rules
    iptables-restore < "$rules_file" 2>/dev/null
    ok "Rules restored from backup"
}

# Check persistence status
check_persistence_status() {
    title "🔍 持久化状态检查"

    local backup_dir="/etc/natmap"
    local rules_file="$backup_dir/iptables.rules"
    local init_script="/etc/init.d/natmap"

    separator

    # Check backup directory
    if [ -d "$backup_dir" ]; then
        printf "  ✅ 备份目录: ${GREEN}存在${NC} ($backup_dir)\n"
    else
        printf "  ❌ 备份目录: ${RED}不存在${NC} ($backup_dir)\n"
    fi

    # Check rules backup file
    if [ -f "$rules_file" ]; then
        local file_size=$(ls -lh "$rules_file" | awk '{print $5}')
        local file_date=$(ls -l "$rules_file" | awk '{print $6, $7, $8}')
        printf "  ✅ 规则备份: ${GREEN}存在${NC} ($file_size, $file_date)\n"

        # Check if backup contains NatMap rules
        local natmap_rules=$(grep -c "BATCH_NAT\|BATCH_FILTER" "$rules_file" 2>/dev/null || echo "0")
        if [ "$natmap_rules" -gt 0 ]; then
            printf "  ✅ NatMap规则: ${GREEN}$natmap_rules 条规则已备份${NC}\n"
        else
            printf "  ⚠️  NatMap规则: ${YELLOW}备份中未发现NatMap规则${NC}\n"
        fi
    else
        printf "  ❌ 规则备份: ${RED}不存在${NC} ($rules_file)\n"
    fi

    # Check init script
    if [ -f "$init_script" ]; then
        printf "  ✅ 启动脚本: ${GREEN}存在${NC} ($init_script)\n"

        # Check if service is enabled
        if [ -L "/etc/rc.d/S99natmap" ]; then
            printf "  ✅ 自动启动: ${GREEN}已启用${NC}\n"
        else
            printf "  ⚠️  自动启动: ${YELLOW}未启用${NC}\n"
        fi
    else
        printf "  ❌ 启动脚本: ${RED}不存在${NC} ($init_script)\n"
    fi

    # Check current rules
    local current_rules=$(iptables -t nat -L BATCH_NAT -n 2>/dev/null | grep -c "DNAT" 2>/dev/null || echo "0")

    # Validate the result is a number using safe_calc
    current_rules=$(safe_calc "$current_rules" 0)

    if [ "$current_rules" -gt 0 ] 2>/dev/null; then
        printf "  ✅ 当前规则: ${GREEN}$current_rules 条活动规则${NC}\n"
    else
        printf "  ⚠️  当前规则: ${YELLOW}无活动规则${NC}\n"
    fi

    separator

    # Overall status
    if [ -f "$rules_file" ] && [ -f "$init_script" ] && [ -L "/etc/rc.d/S99natmap" ]; then
        printf "\n${GREEN}🎉 持久化状态: 完全配置${NC}\n"
        printf "   重启后规则将自动恢复\n"
    elif [ -f "$rules_file" ] && [ -f "$init_script" ]; then
        printf "\n${YELLOW}⚠️  持久化状态: 部分配置${NC}\n"
        printf "   需要启用自动启动: /etc/init.d/natmap enable\n"
    else
        printf "\n${RED}❌ 持久化状态: 未配置${NC}\n"
        printf "   重启后规则将丢失，请运行保存规则功能\n"
    fi
}

# Disable persistence - remove all persistence files
disable_persistence() {
    title "🚫 禁用持久化功能"

    local backup_dir="/etc/natmap"
    local rules_file="$backup_dir/iptables.rules"
    local init_script="/etc/init.d/natmap"
    local startup_link="/etc/rc.d/S99natmap"

    separator
    printf "${YELLOW}⚠️  即将删除以下持久化文件:${NC}\n\n"

    # Check what will be removed
    local files_to_remove=""
    local file_count=0

    if [ -f "$rules_file" ]; then
        printf "  📄 规则备份文件: ${RED}$rules_file${NC}\n"
        files_to_remove="$files_to_remove $rules_file"
        file_count=$((file_count + 1))
    fi

    if [ -f "$init_script" ]; then
        printf "  🔧 启动脚本: ${RED}$init_script${NC}\n"
        files_to_remove="$files_to_remove $init_script"
        file_count=$((file_count + 1))
    fi

    if [ -L "$startup_link" ]; then
        printf "  🔗 自动启动链接: ${RED}$startup_link${NC}\n"
        files_to_remove="$files_to_remove $startup_link"
        file_count=$((file_count + 1))
    fi

    if [ -d "$backup_dir" ]; then
        printf "  📁 备份目录: ${RED}$backup_dir${NC}\n"
        files_to_remove="$files_to_remove $backup_dir"
        file_count=$((file_count + 1))
    fi

    if [ $file_count -eq 0 ]; then
        printf "  ${GREEN}✓ 没有发现持久化文件，系统已经是非持久化状态${NC}\n"
        separator
        return 0
    fi

    printf "\n${RED}⚠️  注意事项:${NC}\n"
    printf "  • 删除后重启将不会自动恢复端口映射规则\n"
    printf "  • 当前内存中的规则仍然有效，直到重启或手动删除\n"
    printf "  • 如需重新启用持久化，可使用菜单选项11保存规则\n"
    separator

    printf "\n确认禁用持久化功能? 输入 ${WHITE}DISABLE${NC} 确认，其他任意键取消: "
    read confirm

    if [ "$confirm" = "DISABLE" ]; then
        printf "\n${CYAN}🗑️  正在删除持久化文件...${NC}\n"

        # Disable service first
        if [ -f "$init_script" ]; then
            /etc/init.d/natmap disable 2>/dev/null || true
            printf "  ✓ 已禁用自动启动服务\n"
        fi

        # Remove startup link
        if [ -L "$startup_link" ]; then
            rm -f "$startup_link" && printf "  ✓ 已删除自动启动链接\n"
        fi

        # Remove init script
        if [ -f "$init_script" ]; then
            rm -f "$init_script" && printf "  ✓ 已删除启动脚本\n"
        fi

        # Remove rules backup
        if [ -f "$rules_file" ]; then
            rm -f "$rules_file" && printf "  ✓ 已删除规则备份文件\n"
        fi

        # Remove backup directory if empty
        if [ -d "$backup_dir" ]; then
            rmdir "$backup_dir" 2>/dev/null && printf "  ✓ 已删除备份目录\n" || printf "  ⚠️  备份目录非空，保留\n"
        fi

        printf "\n${GREEN}🎉 持久化功能已完全禁用${NC}\n"
        printf "${CYAN}💡 提示:${NC}\n"
        printf "  • 当前端口映射规则仍然有效\n"
        printf "  • 重启后规则将不会自动恢复\n"
        printf "  • 如需重新启用，使用菜单选项11保存规则\n"

    else
        info "❌ 禁用操作已取消"
    fi
}



# Load configuration from INI format (unified multi-network support)
load_config() {
    if [ ! -f "$CONFIG_FILE" ]; then
        warn "Configuration file not found: $CONFIG_FILE"
        return 1
    fi

    # Clear any existing network configurations
    unset NETWORKS
    NETWORK_COUNT=0

    # Parse INI format configuration
    local current_section=""
    local network_id=""

    while IFS= read -r line; do
        # Skip comments and empty lines
        [ -z "$line" ] && continue
        echo "$line" | grep -q "^[;#]" && continue

        # Check for section headers [network_X]
        if echo "$line" | grep -q "^\[network_[0-9]*\]"; then
            current_section=$(echo "$line" | sed 's/\[\(.*\)\]/\1/')
            network_id=$(echo "$current_section" | sed 's/network_//')
            continue
        fi

        # Skip non-network sections
        [ -z "$network_id" ] && continue
        echo "$current_section" | grep -q "^network_" || continue

        # Parse key-value pairs
        if echo "$line" | grep -q "="; then
            local key=$(echo "$line" | cut -d'=' -f1 | tr -d ' ')
            local value=$(echo "$line" | cut -d'=' -f2- | sed 's/^[ "]*//;s/[ "]*$//')

            case "$key" in
                "network")
                    eval "NETWORK_${network_id}_NETWORK=\"$value\""
                    ;;
                "wan_interface")
                    eval "NETWORK_${network_id}_WAN_INTERFACE=\"$value\""
                    ;;
                "start_ip")
                    # Extract just the IP number from full IP
                    local start_ip_num=$(echo "$value" | cut -d'.' -f4)
                    eval "NETWORK_${network_id}_START_IP=\"$start_ip_num\""
                    ;;
                "end_ip")
                    # Extract just the IP number from full IP
                    local end_ip_num=$(echo "$value" | cut -d'.' -f4)
                    eval "NETWORK_${network_id}_END_IP=\"$end_ip_num\""
                    ;;
                "start_port")
                    eval "NETWORK_${network_id}_START_PORT=\"$value\""
                    ;;
                "ports_per_vm")
                    eval "NETWORK_${network_id}_PORTS_PER_VM=\"$value\""
                    ;;
                "services")
                    eval "NETWORK_${network_id}_SERVICES=\"$value\""
                    ;;
                "public_host")
                    eval "NETWORK_${network_id}_PUBLIC_HOST=\"$value\""
                    ;;
            esac
        fi
    done < "$CONFIG_FILE"

    # Count valid network configurations
    local i=1
    while [ $i -le 10 ]; do  # Check up to 10 networks
        local network_var="NETWORK_${i}_NETWORK"
        if eval "[ -n \"\$$network_var\" ]"; then
            NETWORK_COUNT=$((NETWORK_COUNT + 1))
        fi
        i=$((i + 1))
    done

    if [ "$NETWORK_COUNT" -eq 0 ]; then
        err "No valid network configurations found in $CONFIG_FILE"
        return 1
    fi

    info "Loaded $NETWORK_COUNT network configurations"
    return 0
}



# Save configuration in INI format (unified multi-network support)
save_config() {
    local network_id="$1" network="$2" wan_interface="$3" start_ip="$4" end_ip="$5" start_port="$6" ports_per_vm="$7" services="$8" public_host="$9"

    # Create or update configuration file in INI format
    if [ ! -f "$CONFIG_FILE" ]; then
        cat > "$CONFIG_FILE" << EOF
; NatMap Configuration File
; Generated by NatMaper v2.1 Multi-Network Support
; Updated: $(get_timestamp)

[mode]
type = 2

EOF
    fi

    # Check if network section already exists and update it
    if grep -q "^\[network_${network_id}\]" "$CONFIG_FILE"; then
        # Update existing network configuration
        local temp_file="/tmp/multi_config_$$"
        local in_section=false

        while IFS= read -r line; do
            if [ "$line" = "[network_${network_id}]" ]; then
                in_section=true
                echo "$line" >> "$temp_file"
                # Write updated configuration
                echo "network = \"$network\"" >> "$temp_file"
                echo "wan_interface = \"$wan_interface\"" >> "$temp_file"
                echo "start_ip = \"$network.$start_ip\"" >> "$temp_file"
                echo "end_ip = \"$network.$end_ip\"" >> "$temp_file"
                echo "start_port = $start_port" >> "$temp_file"
                echo "ports_per_vm = $ports_per_vm" >> "$temp_file"
                echo "services = \"$services\"" >> "$temp_file"
                echo "public_host = \"$public_host\"" >> "$temp_file"
                echo "network_segment = \"$network.0/24\"" >> "$temp_file"
                continue
            elif [ "$in_section" = true ] && echo "$line" | grep -q "^\["; then
                in_section=false
                echo "" >> "$temp_file"
                echo "$line" >> "$temp_file"
            elif [ "$in_section" = false ]; then
                echo "$line" >> "$temp_file"
            fi
        done < "$CONFIG_FILE"

        mv "$temp_file" "$CONFIG_FILE"
        ok "Updated network $network_id configuration in $CONFIG_FILE"
    else
        # Add new network configuration
        cat >> "$CONFIG_FILE" << EOF

[network_${network_id}]
network = "$network"
wan_interface = "$wan_interface"
start_ip = "$network.$start_ip"
end_ip = "$network.$end_ip"
start_port = $start_port
ports_per_vm = $ports_per_vm
services = "$services"
public_host = "$public_host"
network_segment = "$network.0/24"
EOF
        ok "Added network $network_id configuration to $CONFIG_FILE"
    fi
}

# Add network to configuration
add_network_to_config() {
    local network="$1" start_ip="$2" end_ip="$3" start_port="$4" ports_per_vm="$5" services="$6" public_host="$7" wan_interface="${8:-wan}"

    # Find next available network ID
    local network_id=1
    if [ -f "$CONFIG_FILE" ]; then
        local max_id=$(grep "^\[network_" "$CONFIG_FILE" | sed 's/\[network_\([0-9]*\)\]/\1/' | sort -n | tail -1)
        [ -n "$max_id" ] && network_id=$((max_id + 1))
    fi

    save_config "$network_id" "$network" "$wan_interface" "$start_ip" "$end_ip" "$start_port" "$ports_per_vm" "$services" "$public_host"
    info "Network assigned ID: $network_id"
    return 0
}

# Remove network from configuration file
remove_network_from_config() {
    local network_prefix="$1"

    if [ ! -f "$CONFIG_FILE" ]; then
        return 0
    fi

    # Find network ID for the given network prefix
    local network_ids=$(grep -B1 "^network = \"$network_prefix\"" "$CONFIG_FILE" | grep "^\[network_" | sed 's/\[network_\([0-9]*\)\]/\1/')

    if [ -z "$network_ids" ]; then
        return 0
    fi

    local temp_file="/tmp/config_cleanup_$$"
    local in_target_section=false
    local removed_count=0

    while IFS= read -r line; do
        # Check if we're entering a target network section
        local is_target=false
        for network_id in $network_ids; do
            if [ "$line" = "[network_${network_id}]" ]; then
                is_target=true
                break
            fi
        done

        if [ "$is_target" = true ]; then
            in_target_section=true
            removed_count=$((removed_count + 1))
            continue
        elif [ "$in_target_section" = true ] && echo "$line" | grep -q "^\["; then
            # Exiting target section, entering new section
            in_target_section=false
            echo "$line" >> "$temp_file"
        elif [ "$in_target_section" = false ]; then
            # Not in target section, keep the line
            echo "$line" >> "$temp_file"
        fi
        # If in_target_section=true and not a new section, skip the line (delete it)
    done < "$CONFIG_FILE"

    mv "$temp_file" "$CONFIG_FILE"

    if [ "$removed_count" -gt 0 ]; then
        info "🗑️ 已从配置文件中移除 $removed_count 个网段配置 ($network_prefix)"
    fi
}

# Update network configuration after partial deletion
update_network_config_range() {
    local network_prefix="$1" new_start_ip="$2" new_end_ip="$3"

    if [ ! -f "$CONFIG_FILE" ]; then
        return 0
    fi

    # Find network ID for the given network prefix
    local network_id=$(grep -B1 "^network = \"$network_prefix\"" "$CONFIG_FILE" | grep "^\[network_" | sed 's/\[network_\([0-9]*\)\]/\1/' | head -1)

    if [ -z "$network_id" ]; then
        return 0
    fi

    # Get current configuration
    local wan_interface=$(grep -A 10 "^\[network_$network_id\]" "$CONFIG_FILE" | grep '^wan_interface' | cut -d'=' -f2 | tr -d ' "')
    local start_port=$(grep -A 10 "^\[network_$network_id\]" "$CONFIG_FILE" | grep '^start_port' | cut -d'=' -f2 | tr -d ' ')
    local ports_per_vm=$(grep -A 10 "^\[network_$network_id\]" "$CONFIG_FILE" | grep '^ports_per_vm' | cut -d'=' -f2 | tr -d ' ')
    local services=$(grep -A 10 "^\[network_$network_id\]" "$CONFIG_FILE" | grep '^services' | cut -d'=' -f2 | tr -d ' "')
    local public_host=$(grep -A 10 "^\[network_$network_id\]" "$CONFIG_FILE" | grep '^public_host' | cut -d'=' -f2 | tr -d ' "')

    # Update with new range
    save_config "$network_id" "$network_prefix" "$wan_interface" "$new_start_ip" "$new_end_ip" "$start_port" "$ports_per_vm" "$services" "$public_host"
    info "📝 已更新网段 $network_prefix 配置范围: $network_prefix.$new_start_ip - $network_prefix.$new_end_ip"
}

# Core algorithm: Calculate VM start port (CRITICAL - must match PHP)
get_vm_start_port() {
    local vm_id="$1"
    echo $((START_PORT + (vm_id - START_IP) * PORTS_PER_VM))
}

# Multi-network algorithm: Calculate VM start port for specific network
get_vm_start_port_multi() {
    local vm_ip="$1"
    local network_prefix=$(echo "$vm_ip" | cut -d'.' -f1-3)
    local vm_id=$(echo "$vm_ip" | cut -d'.' -f4)

    # Find matching network configuration
    local i=1
    while [ $i -le $NETWORK_COUNT ]; do
        eval "local net=\$NETWORK_${i}_NETWORK"
        eval "local start_ip=\$NETWORK_${i}_START_IP"
        eval "local start_port=\$NETWORK_${i}_START_PORT"
        eval "local ports_per_vm=\$NETWORK_${i}_PORTS_PER_VM"

        if [ "$network_prefix" = "$net" ]; then
            echo $((start_port + (vm_id - start_ip) * ports_per_vm))
            return 0
        fi
        i=$((i + 1))
    done

    # Fallback to single network mode if no match found
    echo $((START_PORT + (vm_id - START_IP) * PORTS_PER_VM))
}

# Get network configuration for specific VM IP
get_network_config() {
    local vm_ip="$1"
    local network_prefix=$(echo "$vm_ip" | cut -d'.' -f1-3)

    # Find matching network configuration
    local i=1
    while [ $i -le $NETWORK_COUNT ]; do
        eval "local net=\$NETWORK_${i}_NETWORK"
        if [ "$network_prefix" = "$net" ]; then
            eval "echo \$NETWORK_${i}_START_IP \$NETWORK_${i}_END_IP \$NETWORK_${i}_START_PORT \$NETWORK_${i}_PORTS_PER_VM \"\$NETWORK_${i}_SERVICES\""
            return 0
        fi
        i=$((i + 1))
    done

    # Return empty if no match found
    echo ""
}

# Extract VM ID from IP address
get_vm_id() {
    local vm_ip="$1"
    echo "$vm_ip" | cut -d'.' -f4
}

# Batch add with enhanced progress display
batch_add() {
    local network="$1" start_ip="$2" end_ip="$3" start_port="$4" ports_per_vm="$5" services="$6" public_host="$7"
    local wan_interface="${8:-wan}"  # Default to 'wan' if not specified for backward compatibility

    add_network_to_config "$network" "$start_ip" "$end_ip" "$start_port" "$ports_per_vm" "$services" "$public_host" "$wan_interface"
    load_config
    init_chains

    local success=0 total=$(safe_calc "$end_ip - $start_ip + 1" 1)
    local current_ip=$start_ip
    
    printf "${CYAN}Creating port forwarding rules:${NC}\n"
    printf "  Network: %s\n" "$network"
    printf "  VM range: %s.%s - %s.%s (%s VMs)\n" "$network" "$start_ip" "$network" "$end_ip" "$total"
    local end_port=$(safe_calc "$start_port + $total * $ports_per_vm - 1" "$start_port")
    local total_rules=$(safe_calc "$total * $ports_per_vm" "$ports_per_vm")
    printf "  Port range: %s - %s (%s ports per VM)\n" "$start_port" "$end_port" "$ports_per_vm"
    printf "  Services: %s\n" "${services:-all direct ports}"
    printf "  Total rules: %s\n" "$total_rules"
    printf "\nStarting configuration...\n"
    
    while [ $current_ip -le $end_ip ]; do
        local vm_ip="$network.$current_ip"
        local vm_start_port=$(get_vm_start_port $current_ip)
        local vm_end_port=$((vm_start_port + ports_per_vm - 1))
        
        printf "${CYAN}VM-$current_ip (%s): %s-%s${NC}\n" "$vm_ip" "$vm_start_port" "$vm_end_port"
        
        local port_success=0 port_index=0 current_port=$vm_start_port
        
        # Add service ports first (SSH, HTTP, etc.)
        if [ -n "$services" ]; then
            for service in $services; do
                local internal_port=$(echo "$service" | cut -d':' -f1)
                local service_name=$(echo "$service" | cut -d':' -f2)
                
                # Add both TCP and UDP rules with WAN interface binding
                if add_port_forwarding_rule "$wan_interface" "$current_port" "$vm_ip" "$internal_port" "single" "$service_name"; then
                    printf "  ✓ %s: %s→%s\n" "$service_name" "$current_port" "$internal_port"
                    port_success=$((port_success + 1))
                fi
                
                current_port=$((current_port + 1))
                port_index=$((port_index + 1))
            done
        fi
        
        # Add remaining direct ports
        local direct_count=$((ports_per_vm - port_index))
        if [ $direct_count -gt 0 ]; then
            local direct_success=0
            while [ $port_index -lt $ports_per_vm ]; do
                # Add both TCP and UDP rules with WAN interface binding
                if add_port_forwarding_rule "$wan_interface" "$current_port" "$vm_ip" "$current_port" "single" "port"; then
                    direct_success=$((direct_success + 1))
                    port_success=$((port_success + 1))
                fi
                current_port=$((current_port + 1))
                port_index=$((port_index + 1))
            done
            printf "  ✓ Direct: %s/%s ports\n" "$direct_success" "$direct_count"
        fi
        
        [ $port_success -gt 0 ] && success=$((success + 1))
        current_ip=$((current_ip + 1))
    done
    
    ok "Completed: $success/$total VMs configured"

    # 自动保存规则以实现持久化
    if [ "$success" -gt 0 ]; then
        printf "\n${CYAN}💾 正在保存规则以实现重启后持久化...${NC}\n"
        save_rules
    fi
}

# Multi-network batch add - process all networks from config
batch_add_multi() {
    if ! load_config; then
        err "Failed to load configuration"
        return 1
    fi

    init_chains

    local total_networks=$NETWORK_COUNT
    local total_success=0
    local total_vms=0
    local total_rules=0

    title "Multi-Network Batch Port Forwarding"
    printf "Processing %s network segments...\n\n" "$total_networks"

    local i=1
    while [ $i -le $NETWORK_COUNT ]; do
        eval "local network=\$NETWORK_${i}_NETWORK"
        eval "local wan_interface=\$NETWORK_${i}_WAN_INTERFACE"
        eval "local start_ip=\$NETWORK_${i}_START_IP"
        eval "local end_ip=\$NETWORK_${i}_END_IP"
        eval "local start_port=\$NETWORK_${i}_START_PORT"
        eval "local ports_per_vm=\$NETWORK_${i}_PORTS_PER_VM"
        eval "local services=\$NETWORK_${i}_SERVICES"
        eval "local public_host=\$NETWORK_${i}_PUBLIC_HOST"

        # Default WAN interface if not specified
        [ -z "$wan_interface" ] && wan_interface="wan"

        local vm_count=$(safe_calc "$end_ip - $start_ip + 1" 1)
        local network_rules=$(safe_calc "$vm_count * $ports_per_vm" "$ports_per_vm")

        printf "${CYAN}Network $i: %s.x (WAN: %s)${NC}\n" "$network" "$wan_interface"
        printf "  VM range: %s.%s - %s.%s (%s VMs)\n" "$network" "$start_ip" "$network" "$end_ip" "$vm_count"
        local end_port=$(safe_calc "$start_port + $network_rules - 1" "$start_port")
        printf "  Port range: %s - %s (%s ports per VM)\n" "$start_port" "$end_port" "$ports_per_vm"
        printf "  Services: %s\n" "${services:-all direct ports}"
        printf "  Rules: %s\n" "$network_rules"

        local network_success=0
        local current_ip=$start_ip

        while [ $current_ip -le $end_ip ]; do
            local vm_ip="$network.$current_ip"
            local vm_start_port=$((start_port + (current_ip - start_ip) * ports_per_vm))
            local vm_end_port=$((vm_start_port + ports_per_vm - 1))

            local port_success=0 port_index=0 current_port=$vm_start_port

            # Add service ports first
            if [ -n "$services" ]; then
                for service in $services; do
                    local internal_port=$(echo "$service" | cut -d':' -f1)

                    # Add both TCP and UDP rules with WAN interface binding
                    local service_name=$(echo "$service" | cut -d':' -f2)
                    if add_port_forwarding_rule "$wan_interface" "$current_port" "$vm_ip" "$internal_port" "network_$i" "$service_name"; then
                        port_success=$((port_success + 1))
                    fi

                    current_port=$((current_port + 1))
                    port_index=$((port_index + 1))
                done
            fi

            # Add remaining direct ports
            while [ $port_index -lt $ports_per_vm ]; do
                # Add both TCP and UDP rules with WAN interface binding
                if add_port_forwarding_rule "$wan_interface" "$current_port" "$vm_ip" "$current_port" "network_$i" "port"; then
                    port_success=$((port_success + 1))
                fi
                current_port=$((current_port + 1))
                port_index=$((port_index + 1))
            done

            [ $port_success -gt 0 ] && network_success=$((network_success + 1))
            current_ip=$((current_ip + 1))
        done

        printf "  ${GREEN}✓ Network $i completed: %s/%s VMs configured${NC}\n\n" "$network_success" "$vm_count"

        total_success=$((total_success + network_success))
        total_vms=$((total_vms + vm_count))
        total_rules=$((total_rules + network_rules))

        i=$((i + 1))
    done

    ok "Multi-network batch completed:"
    info "  Networks processed: $total_networks"
    info "  Total VMs configured: $total_success/$total_vms"
    info "  Total rules created: $total_rules"

    # 自动保存规则以实现持久化
    if [ "$total_success" -gt 0 ]; then
        printf "\n${CYAN}💾 正在保存规则以实现重启后持久化...${NC}\n"
        save_rules
    fi
}

# Display VM information with detailed port mappings
display_vm_info() {
    local vm_ip="$1" vm_start_port="$2" vm_end_port="$3" ssh_port="$4" http_port="$5" https_port="$6" direct_ports="$7" total_count="$8"

    # Validate input parameters
    [ -z "$vm_ip" ] && return 1
    [ -z "$vm_start_port" ] && vm_start_port="0"
    [ -z "$vm_end_port" ] && vm_end_port="$vm_start_port"
    [ -z "$total_count" ] && total_count="0"

    # Format external port range
    local ext_port_range=""
    if [ "$vm_start_port" = "$vm_end_port" ]; then
        ext_port_range="$vm_start_port"
    else
        ext_port_range="$vm_start_port-$vm_end_port"
    fi

    # Format service mappings with internal port information
    local service_mappings=""
    [ -n "$ssh_port" ] && service_mappings="SSH:$ssh_port→22"
    [ -n "$http_port" ] && {
        [ -n "$service_mappings" ] && service_mappings="$service_mappings "
        service_mappings="$service_mappings""HTTP:$http_port→80"
    }
    [ -n "$https_port" ] && {
        [ -n "$service_mappings" ] && service_mappings="$service_mappings "
        service_mappings="$service_mappings""HTTPS:$https_port→443"
    }
    [ -z "$service_mappings" ] && service_mappings="-"

    # Format direct port mappings (external = internal)
    local direct_mappings=""
    if [ -n "$direct_ports" ]; then
        local direct_count=$(echo "$direct_ports" | tr ',' '\n' | wc -l)
        if [ "$direct_count" -le 3 ]; then
            # Show individual mappings for small counts
            local first=1
            for port in $(echo "$direct_ports" | tr ',' ' '); do
                [ $first -eq 0 ] && direct_mappings="$direct_mappings,"
                direct_mappings="$direct_mappings$port→$port"
                first=0
            done
        else
            # Show range for large counts
            local first_direct=$(echo "$direct_ports" | cut -d',' -f1)
            local last_direct=$(echo "$direct_ports" | tr ',' '\n' | tail -1)
            if [ "$((last_direct - first_direct + 1))" = "$direct_count" ]; then
                # Consecutive ports
                direct_mappings="$first_direct-$last_direct→$first_direct-$last_direct"
            else
                # Non-consecutive ports
                direct_mappings="$first_direct...$last_direct→same ($direct_count ports)"
            fi
        fi
    else
        direct_mappings="-"
    fi

    # Print formatted row with enhanced port mapping display
    printf "  ${WHITE}%-15s${NC} %-15s %-35s %-25s %-10s ${CYAN}%s${NC}\n" \
        "$vm_ip" \
        "$ext_port_range" \
        "$service_mappings" \
        "$direct_mappings" \
        "TCP+UDP" \
        "$total_count"
}

# Show current active rules from iptables
show_rules() {
    title "当前端口映射规则"

    if ! iptables -t nat -L BATCH_NAT >/dev/null 2>&1; then
        warn "未找到端口映射链 (BATCH_NAT)"
        printf "\n${CYAN}💡 提示:${NC}\n"
        printf "  • 使用选项1添加端口映射规则\n"
        printf "  • 使用选项8从配置文件恢复规则\n"
        return 1
    fi

    local rules=$(iptables -t nat -L BATCH_NAT -n 2>/dev/null | grep -c "DNAT" 2>/dev/null || echo "0")

    # Validate the result is a number
    if ! echo "$rules" | grep -q '^[0-9]\+$'; then
        rules="0"
    fi

    if [ "$rules" -eq 0 ]; then
        warn "当前无活动规则"
        printf "\n${CYAN}💡 提示:${NC}\n"
        printf "  • 使用选项1添加端口映射规则\n"
        printf "  • 使用选项8从配置文件恢复规则\n"
        return 1
    fi

    printf "总规则数: ${GREEN}%s条${NC} (TCP+UDP)\n" "$rules"

    # Safe arithmetic calculation
    local port_mappings=0
    if [ "$rules" -gt 0 ] 2>/dev/null; then
        port_mappings=$((rules / 2))
    fi
    printf "实际端口映射: ${GREEN}%s个${NC}\n\n" "$port_mappings"

    # Display rules grouped by VM using awk for better performance
    printf "%-15s %-15s %-10s\n" "VM地址" "端口范围" "端口数"
    printf "%-15s %-15s %-10s\n" "---------------" "---------------" "----------"

    # Extract and group rules by VM
    iptables -t nat -L BATCH_NAT -n 2>/dev/null | grep "DNAT" | \
    awk '{
        match($0, /dpt:([0-9]+)/, ext_port);
        match($0, /to:([0-9.]+):([0-9]+)/, target);
        vm_ip = target[1];
        int_port = target[2];
        ext = ext_port[1];

        if (vm_ip && ext) {
            if (!(vm_ip in vms)) {
                vm_order[++vm_count] = vm_ip;
            }
            vms[vm_ip]++;
            if (!(vm_ip in min_port) || ext < min_port[vm_ip]) min_port[vm_ip] = ext;
            if (!(vm_ip in max_port) || ext > max_port[vm_ip]) max_port[vm_ip] = ext;
        }
    }
    END {
        for (i = 1; i <= vm_count; i++) {
            vm = vm_order[i];
            ports = vms[vm] / 2;  # TCP+UDP = 2 rules per port
            if (min_port[vm] == max_port[vm]) {
                range = min_port[vm];
            } else {
                range = min_port[vm] "-" max_port[vm];
            }
            printf "%-15s %-15s %-10d\n", vm, range, ports;
        }
    }'

    printf "\n${CYAN}💡 说明:${NC}\n"
    printf "  • 每个端口同时支持TCP和UDP协议\n"
    printf "  • 端口数 = 实际可用的端口映射数量\n"
    printf "  • 使用选项7可导出当前配置\n"
}



# Analyze current iptables rules to extract configuration
analyze_current_rules() {
    if ! iptables -t nat -L BATCH_NAT >/dev/null 2>&1; then
        return 1
    fi

    local rules=$(iptables -t nat -L BATCH_NAT -n | grep -c "DNAT" || echo "0")
    [ "$rules" -eq 0 ] && return 1

    # Extract VM IPs and ports from current rules
    local temp_analysis="/tmp/rule_analysis_$$"
    iptables -t nat -L BATCH_NAT -n | grep "DNAT" | while read line; do
        local port=$(echo "$line" | sed 's/.*dpt:\([0-9]*\).*/\1/')
        local target=$(echo "$line" | sed 's/.*to:\([0-9.]*:[0-9]*\).*/\1/')
        local vm_ip=$(echo "$target" | cut -d':' -f1)
        local vm_port=$(echo "$target" | cut -d':' -f2)
        echo "$vm_ip|$port|$vm_port"
    done | sort -t'|' -k1,1V -k2,2n > "$temp_analysis"

    # Analyze patterns with correct IP range ordering
    local first_vm_ip=$(head -1 "$temp_analysis" | cut -d'|' -f1)
    local last_vm_ip=$(tail -1 "$temp_analysis" | cut -d'|' -f1)
    local first_port=$(head -1 "$temp_analysis" | cut -d'|' -f2)

    # Extract network and IP range
    NETWORK=$(echo "$first_vm_ip" | cut -d'.' -f1-3)
    local first_vm_id=$(echo "$first_vm_ip" | cut -d'.' -f4)
    local last_vm_id=$(echo "$last_vm_ip" | cut -d'.' -f4)

    # Ensure correct IP range order (smallest to largest)
    if [ "$first_vm_id" -le "$last_vm_id" ]; then
        START_IP="$first_vm_id"
        END_IP="$last_vm_id"
    else
        START_IP="$last_vm_id"
        END_IP="$first_vm_id"
    fi

    # Calculate ports per VM by analyzing first VM (use the actual first VM in sorted order)
    local actual_first_vm="$NETWORK.$START_IP"
    local first_vm_ports=$(grep "^$actual_first_vm|" "$temp_analysis" | wc -l)

    # If the actual first VM has no rules, use any VM to calculate ports per VM
    if [ "$first_vm_ports" -eq 0 ]; then
        first_vm_ports=$(grep "^$first_vm_ip|" "$temp_analysis" | wc -l)
        # Calculate the correct start port based on the algorithm
        local first_vm_start_port=$(grep "^$first_vm_ip|" "$temp_analysis" | head -1 | cut -d'|' -f2)
        local first_vm_id_num=$(echo "$first_vm_ip" | cut -d'.' -f4)
        START_PORT=$((first_vm_start_port - (first_vm_id_num - START_IP) * first_vm_ports))
    else
        # Use the actual first VM's first port
        START_PORT=$(grep "^$actual_first_vm|" "$temp_analysis" | head -1 | cut -d'|' -f2)
    fi

    PORTS_PER_VM="$first_vm_ports"

    # Analyze services
    local ssh_found=$(grep "|22$" "$temp_analysis" | head -1)
    local http_found=$(grep "|80$" "$temp_analysis" | head -1)

    SERVICES=""
    [ -n "$ssh_found" ] && SERVICES="22:SSH"
    [ -n "$http_found" ] && {
        [ -n "$SERVICES" ] && SERVICES="$SERVICES "
        SERVICES="${SERVICES}80:HTTP"
    }

    rm -f "$temp_analysis"

    info "Analyzed configuration:"
    info "  Network: $NETWORK"
    info "  VM range: $START_IP - $END_IP"
    info "  Port range: $START_PORT - $((START_PORT + (END_IP - START_IP + 1) * PORTS_PER_VM - 1))"
    info "  Ports per VM: $PORTS_PER_VM"
    info "  Services: ${SERVICES:-none detected}"

    return 0
}

# Export natmap.conf for PHP query system
export_config() {
    title "Export NatMap Configuration"

    # Load configuration or analyze current rules
    if [ -f "$CONFIG_FILE" ] && load_config 2>/dev/null; then
        info "Configuration detected: $NETWORK_COUNT networks"
    else
        info "No configuration file found, analyzing current iptables rules..."
        if ! analyze_current_rules; then
            err "No iptables rules found to analyze"
            info "Please create rules first using 'Batch add rules' option"
            return 1
        fi
    fi

    # Generate multi-network configuration (unified format)
        cat > "./natmap.conf" << EOF
; NatMap Multi-Network Configuration File
; Generated by NatMaper v2.1 Multi-Network Support
; $(get_timestamp)

[mode]
type = 2

EOF

        # Add each network configuration
        local i=1
        while [ $i -le $NETWORK_COUNT ]; do
            eval "local network=\$NETWORK_${i}_NETWORK"
            eval "local start_ip=\$NETWORK_${i}_START_IP"
            eval "local end_ip=\$NETWORK_${i}_END_IP"
            eval "local start_port=\$NETWORK_${i}_START_PORT"
            eval "local ports_per_vm=\$NETWORK_${i}_PORTS_PER_VM"
            eval "local services=\$NETWORK_${i}_SERVICES"
            eval "local public_host=\$NETWORK_${i}_PUBLIC_HOST"

            # Get WAN interface for this network (default to 'wan' for backward compatibility)
            eval "local wan_interface=\$NETWORK_${i}_WAN_INTERFACE"
            [ -z "$wan_interface" ] && wan_interface="wan"

            # Verify that this network actually has active rules
            local active_rules=$(iptables -t nat -L BATCH_NAT -n 2>/dev/null | grep "$network\." | wc -l)
            if [ "$active_rules" -eq 0 ]; then
                warn "⚠️ 网段 $network 在配置中存在但无活动规则，跳过导出"
                i=$((i + 1))
                continue
            fi

            cat >> "./natmap.conf" << EOF
[network_$i]
network = "$network"
wan_interface = "$wan_interface"
start_ip = "$network.$start_ip"
end_ip = "$network.$end_ip"
start_port = $start_port
ports_per_vm = $ports_per_vm
services = "$services"
public_host = "$public_host"
network_segment = "$network.0/24"

EOF
            i=$((i + 1))
        done

        # Configuration complete - no separate natIPmap section needed
        # Public hosts are now integrated into each network configuration

        ok "Multi-network configuration exported: ./natmap.conf"
        info "Configuration includes $NETWORK_COUNT network segments"



    ok "Configuration exported: ./natmap.conf"
    info "This file is compatible with your existing PHP query system"
    info "You can now use this with your natmap-query.php page"
}

# Unified restore rules function (from config file or backup)
restore_rules_unified() {
    title "🔄 恢复端口映射规则"
    separator
    printf "${CYAN}💡 选择恢复方式:${NC}\n\n"

    printf "  1) 从配置文件恢复 (natmap.conf)\n"
    printf "  2) 从备份文件恢复 (/etc/natmap/iptables.rules)\n"
    printf "  0) 取消\n\n"

    printf "选择恢复方式 [1-2, 0]: "
    read restore_choice

    case "$restore_choice" in
        1)
            printf "\n${CYAN}📥 从配置文件恢复规则${NC}\n"
            separator
            import_config_internal
            ;;
        2)
            printf "\n${CYAN}💾 从备份文件恢复规则${NC}\n"
            separator
            restore_rules_internal
            ;;
        0)
            info "❌ 恢复操作已取消"
            return 0
            ;;
        *)
            err "无效选择"
            return 1
            ;;
    esac
}

# Import natmap.conf and preview rules to be restored
import_config_internal() {
    title "📥 导入 natmap.conf 配置文件"

    # Check if configuration file exists
    if [ ! -f "$CONFIG_FILE" ]; then
        err "❌ 配置文件不存在: $CONFIG_FILE"
        printf "\n${CYAN}💡 请确保以下文件存在:${NC}\n"
        printf "  📄 %s (多网段INI格式配置文件)\n\n" "$CONFIG_FILE"
        printf "${CYAN}💡 如何获取配置文件:${NC}\n"
        printf "  1. 使用选项7导出当前配置: ./NatMaper.sh export\n"
        printf "  2. 从备份中恢复配置文件\n"
        printf "  3. 手动创建符合格式的配置文件\n"
        return 1
    fi

    printf "${CYAN}📄 找到配置文件: %s${NC}\n" "$CONFIG_FILE"
    separator

    # Import multi-network configuration (unified format)
    local mode_type=$(grep '^type' "$CONFIG_FILE" | cut -d'=' -f2 | tr -d ' ')

    if [ "$mode_type" = "2" ]; then
        printf "${CYAN}🌐 导入多网段配置${NC}\n"
        import_multi_network_config
        return $?
    else
        err "❌ 不支持的配置格式"
        printf "\n${CYAN}💡 请使用多网段INI格式的配置文件${NC}\n"
        printf "  配置文件应包含 [mode] 部分，其中 type = 2\n"
        return 1
    fi
}



# Import multi-network configuration
import_multi_network_config() {
    separator

    # Parse multi-network configuration
    local network_count=0
    local total_vms=0
    local total_rules=0

    # Count networks
    network_count=$(grep '^\[network_' "$CONFIG_FILE" | wc -l)

    if [ "$network_count" -eq 0 ]; then
        err "❌ 未找到网段配置"
        return 1
    fi

    printf "${GREEN}✓ 发现 %s 个网段配置${NC}\n\n" "$network_count"

    # Display configuration summary
    printf "${WHITE}📋 多网段配置内容:${NC}\n"
    separator

    local i=1
    while [ $i -le $network_count ]; do
        # Parse network configuration
        local network=$(grep -A 10 "^\[network_$i\]" "$CONFIG_FILE" | grep '^network =' | cut -d'=' -f2 | tr -d ' "')
        local wan_interface=$(grep -A 10 "^\[network_$i\]" "$CONFIG_FILE" | grep '^wan_interface' | cut -d'=' -f2 | tr -d ' "')
        local start_ip=$(grep -A 10 "^\[network_$i\]" "$CONFIG_FILE" | grep '^start_ip' | cut -d'=' -f2 | tr -d ' "')
        local end_ip=$(grep -A 10 "^\[network_$i\]" "$CONFIG_FILE" | grep '^end_ip' | cut -d'=' -f2 | tr -d ' "')
        local start_port=$(grep -A 10 "^\[network_$i\]" "$CONFIG_FILE" | grep '^start_port' | cut -d'=' -f2 | tr -d ' ')
        local ports_per_vm=$(grep -A 10 "^\[network_$i\]" "$CONFIG_FILE" | grep '^ports_per_vm' | cut -d'=' -f2 | tr -d ' ')
        local services=$(grep -A 10 "^\[network_$i\]" "$CONFIG_FILE" | grep '^services' | cut -d'=' -f2 | tr -d ' "')
        local public_host=$(grep -A 10 "^\[network_$i\]" "$CONFIG_FILE" | grep '^public_host' | cut -d'=' -f2 | tr -d ' "')

        # Default WAN interface if not specified
        [ -z "$wan_interface" ] && wan_interface="wan"

        # Extract IP numbers
        local start_ip_num=$(echo "$start_ip" | cut -d'.' -f4)
        local end_ip_num=$(echo "$end_ip" | cut -d'.' -f4)

        # Calculate statistics
        local vm_count=$((end_ip_num - start_ip_num + 1))
        local network_rules=$((vm_count * ports_per_vm))
        local end_port=$((start_port + network_rules - 1))

        printf "  ${CYAN}网段 #%s:${NC}\n" "$i"
        printf "    🌐 网络: %s.x (%s - %s, %s个VM)\n" "$network" "$start_ip" "$end_ip" "$vm_count"
        printf "    🔌 WAN接口: %s\n" "$wan_interface"
        printf "    ⚡ 端口: %s - %s (%s个端口/VM)\n" "$start_port" "$end_port" "$ports_per_vm"
        printf "    🔧 服务: %s\n" "${services:-全部直接映射}"
        printf "    🌍 公网: %s\n" "$public_host"
        printf "    📊 规则: %s条\n" "$network_rules"
        printf "\n"

        total_vms=$((total_vms + vm_count))
        total_rules=$((total_rules + network_rules))

        i=$((i + 1))
    done

    separator
    printf "${WHITE}📊 总计统计:${NC}\n"
    printf "  🌐 网段数量: %s个\n" "$network_count"
    printf "  🎯 VM总数: %s个\n" "$total_vms"
    printf "  📊 规则总数: %s条\n" "$total_rules"
    separator

    # Confirmation
    printf "\n${YELLOW}⚠️  注意事项:${NC}\n"
    printf "  • 此操作将根据配置文件恢复所有网段的端口转发规则\n"
    printf "  • 如果已存在相同规则，可能会产生冲突\n"
    printf "  • 建议在导入前清除现有规则或确认无冲突\n\n"

    printf "确认导入并创建这些规则? 输入 ${WHITE}y${NC} 继续，${WHITE}n${NC} 取消 [y/N]: "
    read confirm

    case "$confirm" in
        [Yy]*)
            printf "\n${CYAN}🚀 开始根据多网段配置文件恢复规则...${NC}\n"
            separator

            # Process each network
            local i=1
            local total_success=0
            while [ $i -le $network_count ]; do
                printf "${CYAN}📍 处理网段 #%s...${NC}\n" "$i"

                # Parse network configuration again
                local network=$(grep -A 10 "^\[network_$i\]" "$CONFIG_FILE" | grep '^network =' | cut -d'=' -f2 | tr -d ' "')
                local wan_interface=$(grep -A 10 "^\[network_$i\]" "$CONFIG_FILE" | grep '^wan_interface' | cut -d'=' -f2 | tr -d ' "')
                local start_ip=$(grep -A 10 "^\[network_$i\]" "$CONFIG_FILE" | grep '^start_ip' | cut -d'=' -f2 | tr -d ' "')
                local end_ip=$(grep -A 10 "^\[network_$i\]" "$CONFIG_FILE" | grep '^end_ip' | cut -d'=' -f2 | tr -d ' "')
                local start_port=$(grep -A 10 "^\[network_$i\]" "$CONFIG_FILE" | grep '^start_port' | cut -d'=' -f2 | tr -d ' ')
                local ports_per_vm=$(grep -A 10 "^\[network_$i\]" "$CONFIG_FILE" | grep '^ports_per_vm' | cut -d'=' -f2 | tr -d ' ')
                local services=$(grep -A 10 "^\[network_$i\]" "$CONFIG_FILE" | grep '^services' | cut -d'=' -f2 | tr -d ' "')
                local public_host=$(grep -A 10 "^\[network_$i\]" "$CONFIG_FILE" | grep '^public_host' | cut -d'=' -f2 | tr -d ' "')

                # Default WAN interface if not specified
                [ -z "$wan_interface" ] && wan_interface="wan"

                # Extract IP numbers
                local start_ip_num=$(echo "$start_ip" | cut -d'.' -f4)
                local end_ip_num=$(echo "$end_ip" | cut -d'.' -f4)

                # Call batch_add for this network with correct WAN interface
                batch_add "$network" "$start_ip_num" "$end_ip_num" "$start_port" "$ports_per_vm" "$services" "$public_host" "$wan_interface"

                # Count success (simplified)
                local vm_count=$((end_ip_num - start_ip_num + 1))
                total_success=$((total_success + vm_count))

                i=$((i + 1))
            done

            separator
            ok "🎉 多网段导入完成: 处理了 $network_count 个网段，涉及 $total_success 个VM"
            info "💡 所有配置的VM现在都可以通过外网端口访问了"
            ;;
        *)
            info "❌ 导入操作已取消"
            ;;
    esac
}

# Flexible delete function with comprehensive options
delete_rules() {
    local type="$1"
    shift

    case "$type" in
        "vm")
            local vm_ip="$1"

            if [ -z "$vm_ip" ]; then
                err "VM IP address required"
                info "Usage examples:"
                info "  delete vm ***************    (deletes all rules for this VM)"
                info "  delete vm ***************    (deletes all rules for this VM)"
                return 1
            fi

            # Validate IP format
            if ! echo "$vm_ip" | grep -q '^[0-9]\{1,3\}\.[0-9]\{1,3\}\.[0-9]\{1,3\}\.[0-9]\{1,3\}$'; then
                err "Invalid IP address format: $vm_ip"
                info "Please use format: ***************"
                return 1
            fi

            local rules=$(iptables -t nat -L BATCH_NAT -n --line-numbers | grep "$vm_ip" | awk '{print $1}' | sort -nr)
            if [ -z "$rules" ]; then
                warn "No port forwarding rules found for VM $vm_ip"
                info "This VM either has no rules or doesn't exist in current configuration"
                return 1
            fi

            local rule_count=$(echo "$rules" | wc -l)
            printf "${YELLOW}About to delete VM rules:${NC}\n"
            printf "  VM IP: %s\n" "$vm_ip"
            printf "  Rules to delete: %s\n" "$rule_count"
            printf "\n${RED}This will permanently delete all port forwarding rules for this VM!${NC}\n"
            printf "Continue? (y/N): "
            read confirm

            case "$confirm" in
                [Yy]*)
                    printf "Deleting %s rules for VM %s...\n" "$rule_count" "$vm_ip"
                    for rule in $rules; do
                        iptables -t nat -D BATCH_NAT $rule 2>/dev/null
                    done
                    # Remove both TCP and UDP filter rules
                    iptables -D BATCH_FILTER -p tcp -d $vm_ip -j ACCEPT 2>/dev/null || true
                    iptables -D BATCH_FILTER -p udp -d $vm_ip -j ACCEPT 2>/dev/null || true

                    # Update configuration file - note that single VM deletion doesn't remove entire network config
                    # We just log the action, full network config remains for other VMs
                    if [ -f "$CONFIG_FILE" ]; then
                        local network_prefix=$(echo "$vm_ip" | cut -d'.' -f1-3)
                        info "📝 注意: 网段 $network_prefix 配置保留，仅删除了VM $vm_ip 的规则"
                    fi

                    ok "VM $vm_ip rules deleted successfully"
                    ;;
                *)
                    info "Deletion cancelled"
                    ;;
            esac
            ;;

        "range")
            local start_ip="$1" end_ip="$2"

            if [ -z "$start_ip" ] || [ -z "$end_ip" ]; then
                err "需要提供起始和结束IP地址"
                printf "\n${CYAN}💡 使用示例:${NC}\n"
                printf "  删除单个网段范围:\n"
                printf "    ${WHITE}./NatMaper.sh delete range *************** ***************${NC}\n"
                printf "    (删除 *************** 到 *************** 共11个VM的所有规则)\n\n"
                printf "  删除大范围:\n"
                printf "    ${WHITE}./NatMaper.sh delete range ********** ***********${NC}\n"
                printf "    (删除 ********** 到 *********** 共50个VM的所有规则)\n\n"
                printf "  删除连续范围:\n"
                printf "    ${WHITE}./NatMaper.sh delete range ************ ************${NC}\n"
                printf "    (删除 ************ 到 ************ 共55个VM的所有规则)\n"
                return 1
            fi

            # Validate IP format
            if ! echo "$start_ip" | grep -q '^[0-9]\{1,3\}\.[0-9]\{1,3\}\.[0-9]\{1,3\}\.[0-9]\{1,3\}$' ||
               ! echo "$end_ip" | grep -q '^[0-9]\{1,3\}\.[0-9]\{1,3\}\.[0-9]\{1,3\}\.[0-9]\{1,3\}$'; then
                err "Invalid IP address format"
                info "Please use full IP addresses like: ***************"
                return 1
            fi

            # Extract network parts
            local start_network=$(echo "$start_ip" | cut -d'.' -f1-3)
            local end_network=$(echo "$end_ip" | cut -d'.' -f1-3)
            local start_host=$(echo "$start_ip" | cut -d'.' -f4)
            local end_host=$(echo "$end_ip" | cut -d'.' -f4)

            # Check if same network
            if [ "$start_network" != "$end_network" ]; then
                err "Start and end IPs must be in the same network segment"
                info "Start network: $start_network.x"
                info "End network: $end_network.x"
                return 1
            fi

            if [ "$start_host" -gt "$end_host" ]; then
                err "Start IP ($start_ip) cannot be greater than end IP ($end_ip)"
                return 1
            fi

            local vm_count=$((end_host - start_host + 1))
            printf "\n${YELLOW}🗑️  准备删除VM范围的端口转发规则${NC}\n"
            separator
            printf "  📍 网络段: ${WHITE}%s.x${NC}\n" "$start_network"
            printf "  🎯 IP范围: ${WHITE}%s${NC} → ${WHITE}%s${NC}\n" "$start_ip" "$end_ip"
            printf "  📊 VM数量: ${WHITE}%s个虚拟机${NC}\n" "$vm_count"
            printf "  ⚡ 预计删除: ${WHITE}约%s条端口转发规则${NC}\n" "$((vm_count * 20))"
            separator
            printf "\n${RED}⚠️  注意: 此操作将永久删除这些VM的所有端口转发规则！${NC}\n"
            printf "${CYAN}💡 提示: 删除后这些VM将无法从外网访问，请确认操作无误${NC}\n\n"
            printf "确认删除? 输入 ${WHITE}y${NC} 继续，${WHITE}n${NC} 取消 [y/N]: "
            read confirm

            case "$confirm" in
                [Yy]*)
                    printf "\n${CYAN}🚀 开始删除VM范围 %s - %s...${NC}\n" "$start_ip" "$end_ip"
                    separator
                    ;;
                *)
                    info "❌ 删除操作已取消"
                    return 0
                    ;;
            esac

            # 优化删除过程：批量收集规则，然后快速删除
            printf "${CYAN}📊 正在分析现有规则...${NC}\n"
            local temp_rules="/tmp/delete_rules_$$"

            # 一次性收集所有需要删除的规则
            iptables -t nat -L BATCH_NAT -n --line-numbers | grep "DNAT" > "$temp_rules"

            local current_host=$start_host total_deleted=0 processed=0
            printf "${CYAN}🗑️  开始批量删除...${NC}\n"

            while [ $current_host -le $end_host ]; do
                local vm_ip="$start_network.$current_host"
                processed=$((processed + 1))

                # 显示进度
                local progress=$((processed * 100 / vm_count))
                printf "\r  [%3d%%] 处理 %s..." "$progress" "$vm_ip"

                # 从预收集的规则中查找匹配的规则
                local rules=$(grep "$vm_ip" "$temp_rules" | awk '{print $1}' | sort -nr)

                if [ -n "$rules" ]; then
                    local rule_count=$(echo "$rules" | wc -l)
                    local count=0

                    # 批量删除规则（更快）
                    for rule in $rules; do
                        iptables -t nat -D BATCH_NAT $rule 2>/dev/null && count=$((count + 1))
                    done
                    # Remove both TCP and UDP filter rules
                    iptables -D BATCH_FILTER -p tcp -d $vm_ip -j ACCEPT 2>/dev/null || true
                    iptables -D BATCH_FILTER -p udp -d $vm_ip -j ACCEPT 2>/dev/null || true

                    total_deleted=$((total_deleted + count))
                    printf " ${GREEN}✓ %s条规则${NC}" "$count"
                else
                    printf " ${CYAN}无规则${NC}"
                fi

                current_host=$((current_host + 1))
            done

            # 清理临时文件
            rm -f "$temp_rules"

            printf "\n"
            separator
            ok "🎉 删除完成: 共删除 $total_deleted 条规则，涉及 $vm_count 个VM"

            # Update configuration file - handle range deletion intelligently
            if [ -f "$CONFIG_FILE" ] && [ "$total_deleted" -gt 0 ]; then
                info "🔄 正在同步配置文件..."

                # Check if we deleted the entire network range
                if [ -f "$CONFIG_FILE" ]; then
                    local network_id=$(grep -B1 "^network = \"$start_network\"" "$CONFIG_FILE" | grep "^\[network_" | sed 's/\[network_\([0-9]*\)\]/\1/' | head -1)

                    if [ -n "$network_id" ]; then
                        local config_start_ip=$(grep -A 10 "^\[network_$network_id\]" "$CONFIG_FILE" | grep '^start_ip' | cut -d'.' -f4 | tr -d ' "')
                        local config_end_ip=$(grep -A 10 "^\[network_$network_id\]" "$CONFIG_FILE" | grep '^end_ip' | cut -d'.' -f4 | tr -d ' "')

                        if [ "$start_host" -le "$config_start_ip" ] && [ "$end_host" -ge "$config_end_ip" ]; then
                            # Deleted entire network range
                            remove_network_from_config "$start_network"
                            info "📝 已从配置中移除整个网段: $start_network"
                        else
                            # Partial deletion - this is complex, just note it
                            info "📝 注意: 网段 $start_network 配置保留，但部分VM规则已删除"
                            info "💡 建议重新配置该网段以保持一致性"
                        fi
                    fi
                fi
            fi

            info "💡 这些VM现在已无法从外网访问，如需恢复请重新配置端口转发"
            ;;

        "port_range")
            local start_port="$1" end_port="$2"

            if [ -z "$start_port" ] || [ -z "$end_port" ]; then
                err "Router port range deletion requires start and end port numbers"
                info "Usage examples:"
                info "  delete port_range 20000 21000    (deletes router external ports 20000-21000)"
                info "  delete port_range 30000 30100    (deletes router external ports 30000-30100)"
                return 1
            fi

            if [ "$start_port" -gt "$end_port" ]; then
                err "Start port ($start_port) cannot be greater than end port ($end_port)"
                return 1
            fi

            local port_count=$((end_port - start_port + 1))
            printf "${YELLOW}About to delete router external port range:${NC}\n"
            printf "  Router external ports: %s - %s\n" "$start_port" "$end_port"
            printf "  Total ports: %s\n" "$port_count"
            printf "\n${RED}This will permanently delete all forwarding rules for these external ports!${NC}\n"
            printf "Continue? (y/N): "
            read confirm

            case "$confirm" in
                [Yy]*)
                    printf "Deleting router external port range %s-%s...\n" "$start_port" "$end_port"
                    ;;
                *)
                    info "Deletion cancelled"
                    return 0
                    ;;
            esac

            local deleted=0
            # Get all rules and filter by port range - Fix: avoid subshell variable scope issue
            local rules_to_delete=$(iptables -t nat -L BATCH_NAT -n --line-numbers | grep "DNAT" | while read line; do
                local rule_num=$(echo "$line" | awk '{print $1}')
                local port=$(echo "$line" | sed 's/.*dpt:\([0-9]*\).*/\1/')

                if [ "$port" -ge "$start_port" ] && [ "$port" -le "$end_port" ]; then
                    echo "$rule_num"
                fi
            done | sort -nr)

            # Delete rules using for loop to properly count deletions
            for rule_num in $rules_to_delete; do
                if [ -n "$rule_num" ] && iptables -t nat -D BATCH_NAT $rule_num 2>/dev/null; then
                    deleted=$((deleted + 1))
                fi
            done

            ok "Port range deleted: $deleted rules removed"
            ;;

        "all")
            printf "${RED}WARNING: This will delete ALL port forwarding rules!${NC}\n"
            printf "${RED}This includes rules for ALL hosts and ALL VMs!${NC}\n"
            printf "\nThis action cannot be undone!\n"
            printf "Type 'CLEAR ALL' to confirm: "
            read confirm

            if [ "$confirm" = "CLEAR ALL" ]; then
                printf "Clearing all port forwarding rules...\n"
                iptables -t nat -F BATCH_NAT 2>/dev/null
                iptables -F BATCH_FILTER 2>/dev/null

                # Clear configuration file as well
                if [ -f "$CONFIG_FILE" ]; then
                    cat > "$CONFIG_FILE" << EOF
; NatMap Configuration File
; Generated by NatMaper v2.1 Multi-Network Support
; Cleared: $(get_timestamp)

[mode]
type = 2

EOF
                    info "🗑️ 配置文件已清空"
                fi

                ok "All rules cleared successfully"
                info "You can now create new configurations from scratch"
            else
                info "Deletion cancelled (confirmation text did not match)"
                info "To confirm, type exactly: CLEAR ALL"
            fi
            ;;

        *)
            err "Invalid delete type"
            info "Available delete types:"
            info "  vm <ip>                    - Delete single VM"
            info "  range <start_ip> <end_ip>  - Delete VM range"
            info "  port_range <start> <end>   - Delete router port range"
            info "  all                        - Delete all rules"
            ;;
    esac
}

# Interactive menu system
show_menu() {
    title "NatMaper v2.0 - Multi-WAN Support"
    printf "  ${WHITE}多网段操作:${NC}\n"
    printf "  1) 多网段批量添加\n"
    printf "  2) 显示当前规则\n"
    printf "\n  ${WHITE}WAN接口管理:${NC}\n"
    printf "  3) 检测WAN接口\n"
    printf "  4) 按接口查询规则\n"
    printf "  5) 按接口删除规则\n"
    printf "  6) 按接口导出规则\n"
    printf "\n  ${WHITE}删除操作:${NC}\n"
    printf "  7) 删除单个VM\n"
    printf "  8) 删除VM范围\n"
    printf "  9) 删除路由器端口范围\n"
    printf "  10) 清除所有规则\n"
    printf "\n  ${WHITE}配置管理:${NC}\n"
    printf "  11) 导出 natmap.conf\n"
    printf "  12) 恢复规则 (配置文件/备份)\n"
    printf "\n  ${WHITE}持久化管理:${NC}\n"
    printf "  13) 保存规则 (重启后生效)\n"
    printf "  14) 查看持久化状态\n"
    printf "  15) 禁用持久化功能\n"
    printf "\n  0) 退出\n\n"
    printf "选择: "
}

# Interactive input functions with enhanced user experience

# Multi-network configuration input
get_multi_network_input() {
    title "🌐 多网段批量配置"
    separator
    printf "${CYAN}💡 配置向导: 为多个网段批量创建端口转发规则${NC}\n\n"

    # Initialize config file if it doesn't exist (don't clear existing)
    if [ ! -f "$CONFIG_FILE" ]; then
        cat > "$CONFIG_FILE" << EOF
; Multi-Network Port Forwarding Configuration
; Generated by NatMaper v2.1 - $(get_timestamp)

[mode]
type = 2

EOF
    else
        info "📄 使用现有配置文件: $CONFIG_FILE"
        # Update timestamp in existing file
        sed -i "1s/.*/; Multi-Network Port Forwarding Configuration/" "$CONFIG_FILE"
        sed -i "2s/.*/; Updated by NatMaper v2.1 - $(get_timestamp)/" "$CONFIG_FILE"
    fi

    # Find the next available network ID from existing config
    local network_count=0
    if [ -f "$CONFIG_FILE" ]; then
        local max_id=$(grep "^\[network_" "$CONFIG_FILE" | sed 's/\[network_\([0-9]*\)\]/\1/' | sort -n | tail -1)
        if [ -n "$max_id" ]; then
            network_count="$max_id"
            printf "${CYAN}📋 现有网段配置:${NC}\n"
            local i=1
            while [ $i -le $max_id ]; do
                local existing_network=$(grep -A 10 "^\[network_$i\]" "$CONFIG_FILE" | grep '^network' | cut -d'=' -f2 | tr -d ' "')
                if [ -n "$existing_network" ]; then
                    printf "  网段 #%s: %s.x\n" "$i" "$existing_network"
                fi
                i=$((i + 1))
            done
            printf "\n${CYAN}💡 将从网段 #%s 开始添加新配置${NC}\n\n" "$((network_count + 1))"
        fi
    fi

    local total_vms=0
    local total_rules=0

    while true; do
        network_count=$((network_count + 1))
        printf "${WHITE}📍 配置网段 #%s${NC}\n" "$network_count"
        separator

        printf "网络前缀 (例: 192.168.222): "
        read network
        validate_input "$network" "网络前缀" || continue

        # WAN interface selection
        printf "\n${CYAN}🔌 WAN接口选择:${NC}\n"
        local available_interfaces=$(detect_wan_interfaces 2>/dev/null | tail -1)
        if [ -n "$available_interfaces" ]; then
            printf "可用物理设备: %s\n" "$available_interfaces"
            printf "${YELLOW}💡 也可使用UCI逻辑名 (如: wan, wan2)${NC}\n"
        fi
        printf "WAN接口名称 (支持UCI逻辑名如: wan, wan2 或物理设备名如: eth0, pppoe-wan, 留空使用默认 'wan'): "
        read wan_interface
        [ -z "$wan_interface" ] && wan_interface="wan"

        # Validate interface if specified
        if [ "$wan_interface" != "wan" ]; then
            if validate_wan_interface "$wan_interface" >/dev/null 2>&1; then
                printf "${GREEN}✓ 接口 '%s' 验证通过${NC}\n" "$wan_interface"
            else
                printf "${YELLOW}⚠ 接口 '%s' 当前不可用，将在配置中保存${NC}\n" "$wan_interface"
            fi
        fi

        printf "\n起始VM编号 (例: 100): "
        read start_ip
        validate_input "$start_ip" "起始IP编号" "^[0-9]\+$" || continue

        printf "结束VM编号 (例: 254): "
        read end_ip
        validate_input "$end_ip" "结束IP编号" "^[0-9]\+$" || continue

        printf "起始外部端口 (例: %s): " "$((20000 + (network_count - 1) * 5000))"
        read start_port
        validate_input "$start_port" "起始端口" "^[0-9]\+$" || continue

        printf "每个VM分配端口数 (例: 20): "
        read ports_per_vm
        validate_input "$ports_per_vm" "每VM端口数" "^[0-9]\+$" || continue

        printf "特殊服务映射 (例: 22:SSH 80:HTTP，留空为全直接映射): "
        read services

        printf "公网地址/域名 (例: nat%s.qq.pw): " "$network_count"
        read public_host
        [ -z "$public_host" ] && public_host="nat${network_count}.qq.pw"

        local vm_count=$((end_ip - start_ip + 1))
        local network_rules=$((vm_count * ports_per_vm))
        local end_port=$((start_port + network_rules - 1))

        printf "\n${CYAN}📊 网段 #%s 配置摘要:${NC}\n" "$network_count"
        printf "  网络: %s.%s - %s.%s (%s个VM)\n" "$network" "$start_ip" "$network" "$end_ip" "$vm_count"
        printf "  WAN接口: %s\n" "$wan_interface"
        printf "  端口: %s - %s (%s个端口/VM)\n" "$start_port" "$end_port" "$ports_per_vm"
        printf "  服务: %s\n" "${services:-全部直接映射}"
        printf "  公网: %s\n" "$public_host"
        printf "  规则: %s条\n" "$network_rules"

        # Save network configuration with WAN interface
        save_config "$network_count" "$network" "$wan_interface" "$start_ip" "$end_ip" "$start_port" "$ports_per_vm" "$services" "$public_host"

        total_vms=$((total_vms + vm_count))
        total_rules=$((total_rules + network_rules))

        printf "\n${GREEN}✓ 网段 #%s 配置已保存${NC}\n\n" "$network_count"

        printf "是否继续添加网段? 输入 ${WHITE}y${NC} 继续，${WHITE}n${NC} 完成配置 [y/N]: "
        read continue_add

        case "$continue_add" in
            [Yy]*) continue ;;
            *) break ;;
        esac
    done

    printf "\n"
    separator
    printf "${YELLOW}📋 多网段配置完成:${NC}\n"
    printf "  配置网段数: %s\n" "$network_count"
    printf "  总VM数量: %s\n" "$total_vms"
    printf "  总规则数: %s\n" "$total_rules"
    separator

    printf "\n确认创建所有网段的端口转发规则? 输入 ${WHITE}y${NC} 继续，${WHITE}n${NC} 取消 [y/N]: "
    read confirm

    case "$confirm" in
        [Yy]*)
            batch_add_multi
            ;;
        *)
            info "❌ 多网段配置已取消"
            ;;
    esac
}

# Show multi-network configuration
show_multi_config() {
    title "🌐 多网段配置查看"

    # Load configuration (unified format)
    if [ ! -f "$CONFIG_FILE" ]; then
        err "❌ 配置文件不存在: $CONFIG_FILE"
        printf "\n${CYAN}💡 请确保以下文件存在:${NC}\n"
        printf "  📄 %s (多网段配置文件)\n\n" "$CONFIG_FILE"
        printf "${CYAN}💡 如何创建配置文件:${NC}\n"
        printf "  1. 使用选项1配置多网段\n"
        printf "  2. 使用选项8导出配置文件\n"
        return 1
    fi

    # Count networks in configuration file
    local network_count=$(grep '^\[network_' "$CONFIG_FILE" | wc -l)

    if [ "$network_count" -eq 0 ]; then
        err "❌ 未找到网段配置"
        return 1
    fi

    info "发现 $network_count 个网段配置"

    separator
    printf "${WHITE}📋 当前多网段配置 (共%s个网段):${NC}\n" "$network_count"
    separator

    local total_vms=0
    local total_rules=0

    local i=1
    while [ $i -le $network_count ]; do
        # Parse network configuration from config file
        local network=$(grep -A 10 "^\[network_$i\]" "$CONFIG_FILE" | grep '^network =' | cut -d'=' -f2 | tr -d ' "')
        local start_ip_full=$(grep -A 10 "^\[network_$i\]" "$CONFIG_FILE" | grep '^start_ip' | cut -d'=' -f2 | tr -d ' "')
        local end_ip_full=$(grep -A 10 "^\[network_$i\]" "$CONFIG_FILE" | grep '^end_ip' | cut -d'=' -f2 | tr -d ' "')
        local start_port=$(grep -A 10 "^\[network_$i\]" "$CONFIG_FILE" | grep '^start_port' | cut -d'=' -f2 | tr -d ' ')
        local ports_per_vm=$(grep -A 10 "^\[network_$i\]" "$CONFIG_FILE" | grep '^ports_per_vm' | cut -d'=' -f2 | tr -d ' ')
        local services=$(grep -A 10 "^\[network_$i\]" "$CONFIG_FILE" | grep '^services' | cut -d'=' -f2 | tr -d ' "')
        local public_host=$(grep -A 10 "^\[network_$i\]" "$CONFIG_FILE" | grep '^public_host' | cut -d'=' -f2 | tr -d ' "')

        # Extract IP numbers from full IP addresses
        local start_ip=$(echo "$start_ip_full" | cut -d'.' -f4)
        local end_ip=$(echo "$end_ip_full" | cut -d'.' -f4)



        # Validate numeric values before arithmetic operations
        if [ -z "$start_ip" ] || [ -z "$end_ip" ] || [ -z "$start_port" ] || [ -z "$ports_per_vm" ]; then
            printf "  ❌ 网段 #%s 配置数据不完整\n" "$i"
            i=$((i + 1))
            continue
        fi

        # Ensure all values are numeric
        if ! echo "$start_ip" | grep -q '^[0-9]\+$' || ! echo "$end_ip" | grep -q '^[0-9]\+$' ||
           ! echo "$start_port" | grep -q '^[0-9]\+$' || ! echo "$ports_per_vm" | grep -q '^[0-9]\+$'; then
            printf "  ❌ 网段 #%s 包含非数字值\n" "$i"
            printf "    起始IP: '%s' %s\n" "$start_ip" "$(echo "$start_ip" | grep -q '^[0-9]\+$' && echo '✓' || echo '❌')"
            printf "    结束IP: '%s' %s\n" "$end_ip" "$(echo "$end_ip" | grep -q '^[0-9]\+$' && echo '✓' || echo '❌')"
            printf "    起始端口: '%s' %s\n" "$start_port" "$(echo "$start_port" | grep -q '^[0-9]\+$' && echo '✓' || echo '❌')"
            printf "    每VM端口: '%s' %s\n" "$ports_per_vm" "$(echo "$ports_per_vm" | grep -q '^[0-9]\+$' && echo '✓' || echo '❌')"
            i=$((i + 1))
            continue
        fi

        local vm_count=$((end_ip - start_ip + 1))
        local network_rules=$((vm_count * ports_per_vm))
        local end_port=$((start_port + network_rules - 1))

        printf "\n${CYAN}网段 #%s:${NC} %s.x\n" "$i" "$network"
        printf "  🎯 VM范围: %s.%s - %s.%s (%s个VM)\n" "$network" "$start_ip" "$network" "$end_ip" "$vm_count"
        printf "  ⚡ 端口范围: %s - %s (%s个端口/VM)\n" "$start_port" "$end_port" "$ports_per_vm"
        printf "  🔧 服务配置: %s\n" "${services:-全部直接映射}"
        printf "  🌐 公网地址: %s\n" "${public_host:-未配置}"
        printf "  📊 规则数量: %s条\n" "$network_rules"

        total_vms=$((total_vms + vm_count))
        total_rules=$((total_rules + network_rules))

        i=$((i + 1))
    done

    printf "\n"
    separator
    printf "${WHITE}📊 总计统计:${NC}\n"
    printf "  网段总数: %s\n" "$network_count"
    printf "  VM总数: %s\n" "$total_vms"
    printf "  规则总数: %s\n" "$total_rules"
    separator

    printf "\n${CYAN}💡 操作提示:${NC}\n"
    printf "  • 使用选项1重新配置多网段\n"
    printf "  • 使用选项8导出配置到natmap.conf\n"
    printf "  • 配置文件位置: %s\n" "$CONFIG_FILE"
}

# Main execution
main() {
    # Handle command line arguments
    if [ $# -gt 0 ]; then
        case "$1" in
            "add")
                check_env
                shift
                if [ $# -ge 5 ]; then
                    batch_add "$1" "$2" "$3" "$4" "$5" "$6" "$7" "${8:-wan}"
                else
                    err "Usage: $0 add <network> <start_ip> <end_ip> <start_port> <ports_per_vm> [services] [public_host] [wan_interface]"
                fi
                ;;
            "show")
                check_env
                show_rules
                ;;
            "delete")
                check_env
                shift
                delete_rules "$@"
                ;;
            "export")
                check_env "export"
                export_config
                ;;
            "import")
                check_env
                import_config_internal
                ;;
            "show_multi_config")
                check_env "show_multi_config"
                show_multi_config
                ;;
            "test_multi")
                check_env "test"
                info "Testing configuration loading..."
                if load_config; then
                    ok "Configuration loaded successfully"
                    info "Found $NETWORK_COUNT network segments"
                else
                    err "Failed to load configuration"
                fi
                ;;
            *)
                err "Unknown command: $1"
                info "Available commands: add, show, delete, export, import, show_multi_config, test_multi"
                ;;
        esac
        return
    fi

    check_env

    # Interactive mode
    while true; do
        show_menu
        read choice

        case "$choice" in
            1) get_multi_network_input ;;
            2) show_rules ;;
            3)
                printf "\n${CYAN}🔍 检测可用WAN接口${NC}\n"
                separator
                detect_wan_interfaces
                ;;
            4)
                printf "\n${CYAN}🔍 按WAN接口查询规则${NC}\n"
                separator
                printf "WAN接口名称 (UCI逻辑名如: wan, wan2 或物理设备名如: eth0, pppoe-wan): "
                read interface
                query_rules_by_interface "$interface"
                ;;
            5)
                printf "\n${CYAN}🗑️  按WAN接口删除规则${NC}\n"
                separator
                printf "WAN接口名称 (UCI逻辑名如: wan, wan2 或物理设备名如: eth0, pppoe-wan): "
                read interface
                delete_rules_by_interface "$interface"
                ;;
            6)
                printf "\n${CYAN}📤 按WAN接口导出规则${NC}\n"
                separator
                printf "WAN接口名称 (UCI逻辑名如: wan, wan2 或物理设备名如: eth0, pppoe-wan): "
                read interface
                printf "输出文件名 (留空使用默认): "
                read output_file
                export_rules_by_interface "$interface" "$output_file"
                ;;
            7)
                printf "\n${CYAN}🗑️  删除单个VM的端口转发规则${NC}\n"
                separator
                printf "${CYAN}💡 示例: 删除 *************** 这个VM的所有端口转发规则${NC}\n"
                printf "VM IP地址 (例: ***************): "
                read vm_ip
                delete_rules vm "$vm_ip"
                ;;
            8)
                printf "\n${CYAN}🗑️  删除VM范围的端口转发规则${NC}\n"
                separator
                printf "${CYAN}💡 示例: 删除 ************** 到 *************** 的所有VM规则${NC}\n"
                printf "起始IP地址 (例: **************): "
                read start_ip
                printf "结束IP地址 (例: ***************): "
                read end_ip
                delete_rules range "$start_ip" "$end_ip"
                ;;
            9)
                printf "\n${CYAN}🗑️  删除路由器外部端口范围${NC}\n"
                separator
                printf "${CYAN}💡 示例: 删除路由器外部端口 20000 到 21000 的所有转发规则${NC}\n"
                printf "起始端口 (例: 20000): "
                read start_port
                printf "结束端口 (例: 21000): "
                read end_port
                delete_rules port_range "$start_port" "$end_port"
                ;;
            10) delete_rules all ;;
            11) export_config ;;
            12) restore_rules_unified ;;
            13)
                printf "\n${CYAN}💾 保存当前规则以实现持久化${NC}\n"
                separator
                save_rules
                ;;
            14) check_persistence_status ;;
            15)
                printf "\n${CYAN}🚫 禁用持久化功能${NC}\n"
                separator
                disable_persistence
                ;;
            0)
                info "Thank you for using NatMaper!"
                exit 0
                ;;
            *)
                err "Invalid choice. Please select 0-15."
                ;;
        esac

        printf "\nPress Enter..."
        read
    done
}

# Execute main function
main "$@"
