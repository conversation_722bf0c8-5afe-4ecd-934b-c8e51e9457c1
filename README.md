# 🌐 NatMap - Network Port Mapping System

A comprehensive port forwarding management system for OpenWrt routers and Proxmox environments with multi-WAN support.

## 📁 Project Structure

- **`NatMaper.sh`** - Main management script with multi-network support
- **`NetMapEnvCheck.sh`** - Environment detection script for OpenWrt 24.x
- **`natmap.php`** - Web query interface with copy functionality
- **`natmap.conf`** - Configuration file (auto-generated)
- **`README.md`** - This documentation file

## 🚀 Quick Start

### 1. Environment Check (Recommended)
```bash
# Make scripts executable
chmod +x NetMapEnvCheck.sh NatMaper.sh

# Check environment compatibility (especially for OpenWrt 24.x)
./NetMapEnvCheck.sh

# If any issues found, follow the fix suggestions provided
```

### 2. Basic Setup
```bash
# Configure port mappings
./NatMaper.sh

# Copy PHP file to web directory
cp natmap.php /www/
```

### 3. Access Web Interface
- **Query Portal**: `http://your-router-ip/natmap.php`

## ✨ Key Features

- **Multi-network segment support** - Handle multiple IP ranges
- **Batch port forwarding** - Efficient rule management
- **Web query interface** - Beautiful, responsive design with copy functionality
- **Privacy protection** - Customers only see their own ports
- **OpenWrt optimized** - Designed for router environments
- **Multi-WAN ready** - Prepared for WAN interface binding upgrade

## 📖 Basic Usage

```bash
# Configure port mappings
./NatMaper.sh

# Export configuration
./NatMaper.sh
# Select option 8: Export natmap.conf

# Copy web interface to router
scp port-query-final.php root@router:/www/
```

## 🔧 Configuration Example

```ini
[mode]
type = 2

[network_1]
network = "192.168.111"
start_ip = "10"
end_ip = "254"
start_port = "20000"
ports_per_vm = "10"
services = "22:SSH"
public_host = "nat1.example.com"
```

## 🚀 OpenWrt Deployment

### 1. Install Dependencies
```bash
# Install iptables if needed
opkg update
opkg install iptables

# Install web server for PHP interfaces
opkg install uhttpd php8 php8-cgi
```

### 2. Deploy Files
```bash
# Copy files to OpenWrt
scp NatMaper.sh root@router:/root/
scp *.php root@router:/www/

# Make executable
chmod +x /root/NatMaper.sh
```

### 3. Configure Web Server
```bash
# Enable PHP in uhttpd
uci set uhttpd.main.interpreter='.php=/usr/bin/php-cgi'
uci commit uhttpd
/etc/init.d/uhttpd restart
```

## 🆘 Troubleshooting

### Common Issues
1. **"Config not found"** - Run `./NatMaper.sh export` first
2. **"Permission denied"** - Check file permissions: `chmod 644 *.php *.conf`
3. **"Firewall reload failed"** - Too many rules, use batch mode
4. **"PHP errors"** - Check web server logs: `logread | grep php`

### Quick Fixes
```bash
# Reset all rules
./NatMaper.sh  # Select option 7

# Regenerate config
./NatMaper.sh  # Select option 8

# Check firewall rules
iptables -t nat -L PREROUTING
```

---

**Version**: 2.0 | **License**: MIT | **Platform**: OpenWrt, Proxmox
