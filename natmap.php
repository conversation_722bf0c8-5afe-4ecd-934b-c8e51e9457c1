<?php
// Port Query - Final Efficient & Beautiful Version
error_reporting(0);

// Load configuration
$config = parse_ini_file('natmap.conf', true);
if (!$config) {
    die('Configuration file not found');
}

// Parse networks and detect mode
$networks = [];
$multi_mode = isset($config['mode']['type']) && $config['mode']['type'] == 2;

if ($multi_mode) {
    foreach ($config as $section => $data) {
        if (strpos($section, 'network_') === 0) {
            $networks[] = $data;
        }
    }
}

// Process query
$query_ip = filter_input(INPUT_GET, 'ip', FILTER_VALIDATE_IP) ?: $_SERVER['REMOTE_ADDR'];
$result = processQuery($query_ip, $networks);

function processQuery($ip, $networks) {
    $parts = explode('.', $ip);
    $network_prefix = implode('.', array_slice($parts, 0, 3));
    $vm_id = (int)$parts[3];
    
    // Check if IP is in private ranges (RFC 1918)
    $private_ranges = [
        '192.168', '10.', '172.16', '172.17', '172.18', '172.19', '172.20',
        '172.21', '172.22', '172.23', '172.24', '172.25', '172.26', '172.27',
        '172.28', '172.29', '172.30', '172.31'
    ];
    
    $is_private = false;
    foreach ($private_ranges as $range) {
        if (strpos($ip, $range) === 0) {
            $is_private = true;
            break;
        }
    }
    
    // If not private IP, it's independent
    if (!$is_private) {
        return ['type' => 'independent', 'ip' => $ip];
    }
    
    // Search in configured networks
    foreach ($networks as $net) {
        $config_network = trim($net['network'] ?? '', '"');
        if ($network_prefix === $config_network) {
            $start_ip = (int)explode('.', trim($net['start_ip'], '"'))[3];
            $end_ip = (int)explode('.', trim($net['end_ip'], '"'))[3];
            
            if ($vm_id >= $start_ip && $vm_id <= $end_ip) {
                $offset = $vm_id - $start_ip;
                $port_start = (int)$net['start_port'] + $offset * (int)$net['ports_per_vm'];
                $host = trim($net['public_host'], '"');
                $services = trim($net['services'] ?? '', '"');
                
                $ports = [];
                $current_port = $port_start;
                
                // Parse services
                if ($services) {
                    foreach (explode(' ', $services) as $svc) {
                        if (strpos($svc, ':')) {
                            list($port, $name) = explode(':', $svc, 2);
                            $ports[] = ['ext' => $current_port, 'int' => (int)$port, 'service' => $name];
                            $current_port++;
                        }
                    }
                }
                
                // Add remaining direct ports
                $remaining = (int)$net['ports_per_vm'] - count($ports);
                for ($i = 0; $i < $remaining; $i++) {
                    $ports[] = ['ext' => $current_port, 'int' => $current_port, 'service' => 'Direct'];
                    $current_port++;
                }
                
                return ['type' => 'mapped', 'ip' => $ip, 'host' => $host, 'ports' => $ports];
            }
        }
    }
    
    // Private IP but not configured
    return ['type' => 'not_configured', 'ip' => $ip];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Port Mapping Query</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e0 100%);
            min-height: 100vh;
            color: #2d3748;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem 1rem;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
            animation: fadeIn 0.8s ease-out;
        }

        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            margin: 0 auto 1.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
            color: white;
        }

        .title {
            font-size: 3.5rem;
            font-weight: 800;
            color: #2d3748;
            margin-bottom: 0.5rem;
        }

        .subtitle {
            color: #64748b;
            font-size: 1.1rem;
        }
        
        .card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            animation: fadeIn 0.8s ease-out 0.2s both;
        }

        .search-form {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .input-field {
            flex: 1;
            padding: 1rem 1.5rem;
            background: rgba(255,255,255,0.9);
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 12px;
            font-size: 1.1rem;
            color: #2d3748;
        }

        .input-field:focus {
            outline: none;
            background: white;
            border-color: #667eea;
        }

        .btn {
            padding: 1rem 2rem;
            border: none;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background: rgba(255,255,255,0.9);
            color: #667eea;
        }

        .btn-primary:hover {
            background: white;
            transform: translateY(-2px);
        }

        .btn-copy {
            background: #48bb78;
            color: white;
            font-size: 0.9rem;
            padding: 0.6rem 1.2rem;
        }

        .btn-copy:hover {
            background: #38a169;
        }
        
        .alert {
            padding: 1.5rem;
            border-radius: 12px;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            animation: fadeInUp 0.6s ease-out 0.4s both;
        }
        
        .alert-success {
            background: linear-gradient(135deg, rgba(72, 187, 120, 0.1), rgba(56, 161, 105, 0.05));
            color: #2f855a;
            border: 1px solid rgba(72, 187, 120, 0.2);
        }
        
        .alert-error {
            background: linear-gradient(135deg, rgba(245, 101, 101, 0.1), rgba(229, 62, 62, 0.05));
            color: #c53030;
            border: 1px solid rgba(245, 101, 101, 0.2);
        }
        
        .alert-icon {
            font-size: 1.5rem;
            flex-shrink: 0;
        }

        .connection-tips {
            margin-top: 1.5rem;
            padding-top: 1rem;
            border-top: 1px solid rgba(72, 187, 120, 0.2);
        }

        .connection-tips h4 {
            margin-bottom: 1rem;
            color: #2f855a;
            font-size: 1rem;
        }

        .tip-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }

        .tip-item {
            background: rgba(72, 187, 120, 0.05);
            border: 1px solid rgba(72, 187, 120, 0.15);
            border-radius: 8px;
            padding: 1rem;
        }

        .tip-item strong {
            display: block;
            margin-bottom: 0.5rem;
            color: #2f855a;
            font-size: 0.9rem;
        }

        .tip-item code {
            display: block;
            background: rgba(72, 187, 120, 0.1);
            border: 1px solid rgba(72, 187, 120, 0.2);
            border-radius: 4px;
            padding: 0.4rem 0.6rem;
            margin: 0.3rem 0;
            font-family: monospace;
            font-size: 0.85rem;
            color: #1a365d;
            word-break: break-all;
        }

        .tip-item small {
            display: block;
            color: #4a5568;
            font-size: 0.75rem;
            margin-top: 0.3rem;
        }
        
        .table-container {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            overflow: hidden;
            animation: fadeIn 0.6s ease-out 0.4s both;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background: #f8fafc;
            color: #374151;
            padding: 1rem;
            text-align: left;
            font-weight: 700;
            font-size: 0.9rem;
            border-bottom: 1px solid #e5e7eb;
        }

        .table td {
            padding: 1rem;
            color: #374151;
            border-top: 1px solid #f3f4f6;
        }

        .table tr:hover {
            background: #f9fafb;
        }

        .port-cell {
            text-align: center;
        }

        .port-code {
            background: #f1f5f9;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 0.3rem 0.6rem;
            font-family: monospace;
            color: #475569;
            font-weight: 600;
        }

        .url-cell {
            font-family: monospace;
            font-size: 0.85rem;
            color: #64748b;
        }

        .action-cell {
            text-align: center;
        }

        .btn-copy-small {
            background: #48bb78;
            border: none;
            border-radius: 6px;
            padding: 0.4rem 0.8rem;
            color: white;
            cursor: pointer;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .btn-copy-small:hover {
            background: #38a169;
        }
        
        .service-tag {
            display: inline-block;
            padding: 0.3rem 0.8rem;
            background: rgba(255,255,255,0.9);
            color: #667eea;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 700;
            text-transform: uppercase;
        }

        .header-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2d3748;
            margin: 0;
        }

        .info-text {
            color: #64748b;
            text-align: center;
        }

        .usage-note {
            margin-top: 1rem;
            padding: 1rem;
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            font-size: 0.9rem;
            color: #475569;
            border-left: 4px solid #667eea;
        }

        @media (max-width: 768px) {
            .container { padding: 1rem; }
            .table-container { overflow-x: auto; }
            .table th, .table td { padding: 0.8rem; font-size: 0.8rem; }
            .title { font-size: 2.5rem; }
            .search-form { flex-direction: column; }
            .card { padding: 1.5rem; }
            .header-row { flex-direction: column; }
            .tip-grid { grid-template-columns: 1fr; }
            .tip-item { padding: 0.8rem; }
            .tip-item code { font-size: 0.8rem; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🌐</div>
            <h1 class="title">Port Mapping Query</h1>
            <p class="subtitle">Discover your server's gateway to the world</p>
        </div>

        <div class="card">
            <form class="search-form">
                <input name="ip" class="input-field" value="<?=htmlspecialchars($query_ip)?>"
                       placeholder="Enter your server's IP address" required>
                <button type="submit" class="btn btn-primary">🔍 Query Ports</button>
            </form>
            <p class="info-text">💡 Enter your server's IP address to discover available port mappings</p>
        </div>

        <?php if ($_GET['ip'] ?? false): ?>
        <div class="card">
            <?php if ($result['type'] === 'independent'): ?>
                <div class="alert alert-success">
                    <div class="alert-icon">🎉</div>
                    <div>
                        <strong>Great! Your Server Has a Public IP</strong><br>
                        Server <code><?=htmlspecialchars($result['ip'])?></code> has a dedicated public IP address. You can connect directly without port mapping!

                        <div class="connection-tips">
                            <h4>💡 Remote Connection:</h4>
                            <div class="tip-grid">
                                <div class="tip-item">
                                    <strong>🐧 Linux SSH:</strong>
                                    <code>ssh user@<?=htmlspecialchars($result['ip'])?></code>
                                    <small>Default port: 22</small>
                                </div>
                                <div class="tip-item">
                                    <strong>🪟 Windows RDP:</strong>
                                    <code><?=htmlspecialchars($result['ip'])?>:3389</code>
                                    <small>Default port: 3389</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php elseif ($result['type'] === 'not_configured'): ?>
                <div class="alert alert-error">
                    <div class="alert-icon">⚠️</div>
                    <div>
                        <strong>No Port Mapping Configuration</strong><br>
                        IP <code><?=htmlspecialchars($result['ip'])?></code> is in a private network but not configured for port mapping.
                    </div>
                </div>
            <?php else: ?>
                <div class="header-row">
                    <h3 class="section-title">🔗 Port Mapping Information</h3>
                    <button type="button" class="btn btn-copy" onclick="copyAllPorts()">📋 Copy All</button>
                </div>
                <p style="margin-bottom: 1.5rem; color: #4a5568; font-size: 1.1rem;">
                    Internal IP: <strong><?=htmlspecialchars($result['ip'])?></strong> →
                    Public Host: <strong><?=htmlspecialchars($result['host'])?></strong>
                </p>

                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Service</th>
                                <th>Internal Port</th>
                                <th>External Port</th>
                                <th>Access URL</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($result['ports'] as $i => $port): ?>
                            <tr>
                                <td><span class="service-tag"><?=htmlspecialchars($port['service'])?></span></td>
                                <td class="port-cell"><code class="port-code"><?=$port['int']?></code></td>
                                <td class="port-cell"><code class="port-code"><?=$port['ext']?></code></td>
                                <td class="url-cell">
                                    <span class="url-text" id="url-<?=$i?>"><?=htmlspecialchars($result['host'])?>:<?=$port['ext']?></span>
                                </td>
                                <td class="action-cell">
                                    <button type="button" class="btn-copy-small" onclick="copyText('<?=htmlspecialchars($result['host'])?>:<?=$port['ext']?>', this)">Copy</button>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <div class="usage-note">
                    <strong>🚀 Usage Guide:</strong> Click individual "Copy" buttons for specific URLs • Use SSH clients for SSH connections • All ports support both TCP and UDP protocols
                </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>
    </div>

    <script>
        async function copyText(text, button) {
            const original = button.textContent;

            try {
                await navigator.clipboard.writeText(text);
                button.textContent = '✓ Copied!';
                button.style.background = '#22c55e';
            } catch (err) {
                // Fallback for older browsers
                const textarea = document.createElement('textarea');
                textarea.value = text;
                textarea.style.position = 'fixed';
                textarea.style.left = '-9999px';
                document.body.appendChild(textarea);
                textarea.select();
                document.execCommand('copy');
                document.body.removeChild(textarea);

                button.textContent = '✓ Copied!';
                button.style.background = '#22c55e';
            }

            setTimeout(() => {
                button.textContent = original;
                button.style.background = '#48bb78';
            }, 1500);
        }

        function copyAllPorts() {
            const host = '<?=htmlspecialchars($result['host'] ?? '')?>';
            let text = '';
            <?php if (isset($result['ports'])): ?>
            <?php foreach ($result['ports'] as $port): ?>
            text += `<?=htmlspecialchars($port['service'])?> (<?=$port['int']?>) → ${host}:<?=$port['ext']?>\n`;
            <?php endforeach; ?>
            <?php endif; ?>
            copyText(text.trim(), event.target);
        }
    </script>
</body>
</html>
